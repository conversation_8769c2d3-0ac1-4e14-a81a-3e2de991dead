<script lang="ts">
	import type { DocumentStatus } from '$lib/schema/literal';

	interface IProps {
		status: DocumentStatus;
		size: 's' | 'm' | 'l';
	}

	const { status = 'Menunggu Verifikasi', size = 'm' }: IProps = $props();

	const sizeNumber = {
		s: 'h-6 text-xs',
		m: 'h-8 text-sm',
		l: 'h-10 text-base'
	};

	const statusColor = {
		'Menunggu Verifikasi': 'bg-amber-400 text-white',
		'Verifikasi Di-Setujui': 'bg-emerald-400 text-white',
		'Verifikasi Di-Tolak': 'bg-rose-500 text-white',
		'Di-Hapus': 'bg-stone-300 text-stone-700'
	} as Record<DocumentStatus, string>;
</script>

<div
	class="rounded-full w-fit px-3 grid place-items-center tracking-wide {sizeNumber[
		size as keyof typeof sizeNumber
	]} {statusColor?.[status as keyof typeof statusColor]}"
>
	{status}
</div>
