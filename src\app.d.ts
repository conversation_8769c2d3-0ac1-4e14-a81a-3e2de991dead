// See https://svelte.dev/docs/kit/types#app.d.ts
// for information about these interfaces
import type { Personil } from '$lib/schema/anggota';
import type {
	AnggaranKegiatan,
	AnggaranTahunan,
	AnggaranTahunanLogInfo
} from '$lib/schema/anggaran';
import type { KategoriPerdin } from '$lib/schema/perdin';

declare global {
	namespace App {
		// interface Error {}
		interface Locals {
			auth: {
				jwt_token: string;
				user: {
					id: number;
					username: string;
					nama: string;
					nip: string;
					email: string;
					role: string;
				};
			};
		}
		interface PageData {
			breadcrumbs?: string[];
			breadcrumbsHref?: string[];

			perdinCategories?: Record<string, KategoriPerdin[]>;

			dprdPersonnel?: Record<string, Personil[]>;
			setwanPersonnel?: Record<string, Personil[]>;

			divisions?: Record<string, AlatKelengkapan[]>;

			annualBudget?: AnggaranTahunan[];
			annualBudgetWithLogInfo?: AnggaranTahunanLogInfo[];

			activityBudget?: AnggaranKegiatan[];
		}
		// interface PageState {}
		// interface Platform {}
	}
}

export {};
