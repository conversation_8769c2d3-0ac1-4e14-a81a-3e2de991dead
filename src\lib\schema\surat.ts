import { Schema } from 'effect';
import {
	_AlatKelengkapan,
	_Created,
	_Simple,
	AlatKelengkapanSchema,
	CreatedSchema,
	SimpleSchema
} from './general';
import { _Personil, PersonilSchema, type Personil } from './anggota';
import { DOCUMENT_IMPORTANCE, DOCUMENT_TYPE } from './literal';
import { getLocalTimeZone, today } from '@internationalized/date';

//

const PersonilSuratSchema = Schema.Struct({
	...PersonilSchema.fields,
	show: Schema.BooleanFromString
}).pipe(Schema.mutable);

export interface PersonilSurat extends Schema.Schema.Type<typeof PersonilSuratSchema> {}
export interface PersonilSuratEncoded extends Schema.Schema.Encoded<typeof PersonilSuratSchema> {}

export const _PersonilSurat: PersonilSurat = {
	..._Personil,
	show: true
};

export const PenomoranSuratSchema = Schema.Struct({
	nomor_1: Schema.NonEmptyString.annotations({
		message: () => 'Nomor Surat tidak boleh kosong'
	}),
	nomor_2: Schema.String,
	nomor_3: Schema.NonEmptyString.annotations({
		message: () => 'Nomor Surat tidak boleh kosong'
	}),
	nomor_4: Schema.NonEmptyString.annotations({
		message: () => 'Nomor Tahun tidak boleh kosong'
	})
});

export interface PenomoranSurat extends Schema.Schema.Type<typeof PenomoranSuratSchema> {}
export interface PenomoranSuratEncoded extends Schema.Schema.Encoded<typeof PenomoranSuratSchema> {}

const _today = today(getLocalTimeZone());

export const _PenomoranSurat: PenomoranSurat = {
	nomor_1: '',
	nomor_2: '',
	nomor_3: '',
	nomor_4: _today.year.toString()
};

export const SuratTugasDewanSchema = Schema.Struct({
	kind: Schema.Literal(DOCUMENT_TYPE[1]),
	ditandatangani_oleh: SimpleSchema,

	...PenomoranSuratSchema.fields,

	dasar: Schema.Array(
		Schema.NonEmptyString.annotations({
			message: () => 'Dasar tidak boleh kosong'
		})
	).pipe(Schema.mutable),

	kepada: Schema.Array(PersonilSuratSchema).pipe(Schema.mutable),
	untuk: Schema.NonEmptyString.annotations({
		message: () => 'Bagian ini tidak boleh kosong'
	}),

	dikeluarkan_di: Schema.NonEmptyString.annotations({
		message: () => 'Bagian ini tidak boleh kosong'
	}),
	pada_tanggal: Schema.NonEmptyString.annotations({
		message: () => 'Bagian ini tidak boleh kosong'
	})
});

export interface SuratTugasDewan extends Schema.Schema.Type<typeof SuratTugasDewanSchema> {}
export interface SuratTugasDewanEncoded
	extends Schema.Schema.Encoded<typeof SuratTugasDewanSchema> {}

export const _SuratTugasDewan: SuratTugasDewan = {
	kind: DOCUMENT_TYPE[1], // Surat Tugas Dewan

	..._PenomoranSurat,

	dasar: [''],
	kepada: [],
	untuk: '',
	dikeluarkan_di: '',
	pada_tanggal: _today.toString(),

	ditandatangani_oleh: _Simple
};

export const SuratTugasSetwanSchema = Schema.Struct({
	kind: Schema.Literal(DOCUMENT_TYPE[2]), // Surat Tugas Setwan

	...PenomoranSuratSchema.fields,
	dasar: Schema.Array(
		Schema.NonEmptyString.annotations({
			message: () => 'Dasar tidak boleh kosong'
		})
	).pipe(Schema.mutable),

	kepada: Schema.Array(PersonilSuratSchema).pipe(Schema.mutable),
	untuk: Schema.NonEmptyString.annotations({
		message: () => 'Bagian ini tidak boleh kosong'
	}),

	pada_tanggal: Schema.NonEmptyString.annotations({
		message: () => 'Bagian ini tidak boleh kosong'
	})
});

export interface SuratTugasSetwan extends Schema.Schema.Type<typeof SuratTugasSetwanSchema> {}
export interface SuratTugasSetwanEncoded
	extends Schema.Schema.Encoded<typeof SuratTugasSetwanSchema> {}

export const _SuratTugasSetwan: SuratTugasSetwan = {
	kind: DOCUMENT_TYPE[2], // Surat Tugas Setwan

	..._PenomoranSurat,

	dasar: [''],
	kepada: [],
	untuk: '',
	pada_tanggal: _today.toString()
};

//

export const NotaDinasSchema = Schema.Struct({
	kind: Schema.Literal(DOCUMENT_TYPE[0]), // Nota Dinas

	alat_kelengkapan: AlatKelengkapanSchema,
	tanggal: Schema.NonEmptyString.annotations({
		message: () => 'Tanggal tidak boleh kosong'
	}),

	...PenomoranSuratSchema.fields,
	sifat: Schema.Literal(...DOCUMENT_IMPORTANCE),
	lampiran: Schema.NumberFromString,
	perihal: Schema.NonEmptyString.annotations({
		message: () => 'Perihal tidak boleh kosong'
	}),

	keterangan_1: Schema.NonEmptyString.annotations({
		message: () => 'Bagian ini tidak boleh kosong'
	}),
	keterangan_2: Schema.NonEmptyString.annotations({
		message: () => 'Bagian ini tidak boleh kosong'
	}),

	peserta: Schema.Array(PersonilSuratSchema).pipe(Schema.mutable)
});

export interface NotaDinas extends Schema.Schema.Type<typeof NotaDinasSchema> {}
export interface NotaDinasEncoded extends Schema.Schema.Encoded<typeof NotaDinasSchema> {}

export const _NotaDinas: NotaDinas = {
	kind: DOCUMENT_TYPE[0], // Nota Dinas

	..._PenomoranSurat,

	alat_kelengkapan: _AlatKelengkapan,

	tanggal: _today.toString(),
	sifat: DOCUMENT_IMPORTANCE[0],
	lampiran: 0,
	perihal: '',

	keterangan_1: '',
	keterangan_2: '',

	peserta: []
};

///////////////////////////////////////////////////////////////////

export const SuratSchema = Schema.Struct({
	id: Schema.NumberFromString,
	tipe_surat: SimpleSchema,
	alat_kelengkapan: SimpleSchema,

	nomor: Schema.NonEmptyString.annotations({
		message: () => 'Nomor Surat tidak boleh kosong'
	}),
	components: Schema.Union(SuratTugasDewanSchema, SuratTugasSetwanSchema, NotaDinasSchema).pipe(
		Schema.mutable
	),
	path: Schema.String,

	perdin: SimpleSchema,
	status_surat: SimpleSchema,

	...CreatedSchema.fields
});

export interface Surat extends Schema.Schema.Type<typeof SuratSchema> {}
export interface SuratEncoded extends Schema.Schema.Encoded<typeof SuratSchema> {}

export const _Surat: Surat = {
	id: 0,
	tipe_surat: _Simple,
	alat_kelengkapan: _Simple,
	nomor: '',
	components: _NotaDinas,
	path: '',
	perdin: _Simple,
	status_surat: _Simple,
	..._Created
};
