<script lang="ts">
	import type { DocumentStatus as DocumentStatusType } from '$lib/schema/literal';
	import DocumentStatus from '../components/document-status.svelte';

	import Separator from '$lib/components/ui/separator/separator.svelte';
	import { Button } from '$lib/components/ui/button/index.js';

	import { getDocumentsOfPerdin } from '$lib/remote/perdin.remote';
	import { getPerdinState } from '../states.svelte';

	import Edit from '@tabler/icons-svelte/icons/edit';
	import Trash from '@tabler/icons-svelte/icons/trash';
	import Pdf from '@lucide/svelte/icons/file-text';

	const state = getPerdinState();
	state.documents = state.perdin ? await getDocumentsOfPerdin(state.perdin.id) : null;
</script>

<h3 class="leading-loose text-stone-600 text-sm tracking-wide">Dokumen Surat - Surat</h3>

{#if state.documents}
	<div class="grid grid-cols-4 gap-2">
		{#each Object.entries(state.documents) as [nama_tipe_surat, _] (nama_tipe_surat)}
			<div class=" pe-2 me-2">
				<h4 class="text-muted-foreground font-semibold text-sm tracking-wide leading-loose mb-2">
					{nama_tipe_surat}
				</h4>

				<div class="flex flex-col gap-2">
					{#each state.documents[nama_tipe_surat] as document (document.id)}
						<div class="rounded-lg border p-4 bg-stone-50 relative">
							<div class="absolute top-2 right-2">
								<DocumentStatus
									status={document.status_surat.nama as DocumentStatusType}
									size="s"
								/>
							</div>

							<p class="text-xs text-muted-foreground font-light">nomor surat :</p>
							<p>{document.nomor}</p>

							<Separator class="my-2" />

							<div class="grid grid-cols-3 gap-2">
								{#if document.status_surat.nama === 'Menunggu Verifikasi' || document.status_surat.nama === 'Verifikasi Di-Tolak'}
									<Button variant="outline" size="sm">
										<Edit /> Sunting
									</Button>
								{/if}

								{#if document.status_surat.nama === 'Verifikasi Di-Setujui'}
									<Button variant="outline" size="sm">
										<Pdf /> Unduh
									</Button>
								{/if}

								{#if document.status_surat.nama === 'Menunggu Verifikasi' || document.status_surat.nama === 'Verifikasi Di-Tolak'}
									<Button variant="outline" class="hover:bg-rose-500 hover:text-white" size="sm">
										<Trash /> Hapus
									</Button>
								{/if}

								{#if document.status_surat.nama === 'Di-Hapus'}
									<p class="col-span-3 font-light">Dokumen telah dihapus.</p>
								{/if}
							</div>
						</div>
					{/each}
				</div>
			</div>
		{/each}
	</div>
{:else}
	<div class="col-span-6 text-destructive">Perdin ini belum memiliki dokumen surat.</div>
{/if}
