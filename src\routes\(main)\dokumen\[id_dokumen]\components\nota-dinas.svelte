<script lang="ts">
	import { _Surat, SuratSchema } from '$lib/schema/surat';
	import * as DocumentRemote from '$lib/remote/document.remote';
	import { Schema } from 'effect';

	import { getDocumentState } from './document-state.svelte';

	setRemoteFormContext(DocumentRemote.create);

	const states = getDocumentState();

	import FormField, { setRemoteFormContext } from '$lib/utils/form-field.svelte';
	import { page } from '$app/state';
	import { DOCUMENT_IMPORTANCE } from '$lib/schema/literal';

	let honorary = $state<{ id: number; nama: string }>({ id: 1, nama: 'Bapak' });
</script>

<form
	id="surat-tugas-dewan-form"
	{...DocumentRemote.create.preflight(SuratSchema.pipe(Schema.standardSchemaV1))}
	oninput={() => DocumentRemote.create.validate()}
>
	{#if states.surat.components.kind === 'Nota Dinas'}
		<div class="flex justify-center items-center">
			{#if page.data.divisions && page.data.divisions.Bagian.length > 0}
				<FormField
					name="components.alat_kelengkapan"
					type="select"
					placeholder="Pilih Bagian Sekretariat Dewan yang Bertanggung Jawab"
					viewClass="uppercase font-bold underline leading-loose"
					bind:value={states.surat.components.alat_kelengkapan}
					bind:mode={states.mode}
					selectOptions={page.data.divisions.Bagian.map((item) => ({
						id: item.id,
						nama: `${item.tipe_alat_kelengkapan.nama} ${item.nama}`
					}))}
				></FormField>
			{/if}
		</div>

		<div class="text-center uppercase underline font-bold leading-loose">Nota Dinas</div>

		<br />

		<div class="grid grid-cols-6 gap-x-2 w-3/4 mx-auto gap-y-1">
			<div class="col-span-1 flex justify-between items-center">
				<span>Kepada</span>
				:
			</div>
			<div class="col-span-5 flex gap-1 items-center">
				<span>Yth. </span>
				<FormField
					name="components.honorary"
					type="select"
					bind:value={honorary}
					bind:mode={states.mode}
					selectOptions={[
						{ id: 1, nama: 'Bapak' },
						{ id: 2, nama: 'Ibu' }
					]}
				/>
				<span>Sekretaris DPRD Kabupaten Sorong</span>
			</div>

			<div class="col-span-1 flex justify-between items-center">
				<span>Dari</span>
				:
			</div>
			<div class="col-span-5">
				Kepala {states.surat.components.alat_kelengkapan?.tipe_alat_kelengkapan?.nama}
				{states.surat.components.alat_kelengkapan?.nama} Sekretariat DPRD Kabupaten Sorong
			</div>

			<div class="col-span-1 flex justify-between items-center">
				<span>Tanggal</span>
				:
			</div>
			<div class="col-span-5">
				<FormField
					name="components.tanggal"
					type="date"
					bind:value={states.surat.components.tanggal}
					bind:mode={states.mode}
				/>
			</div>

			<div class="col-span-1 flex justify-between items-center">
				<span>Nomor</span>
				:
			</div>
			<div class="col-span-5 flex gap-2">
				<FormField
					name="components.nomor_1"
					type="text"
					class="w-16 text-center"
					bind:value={states.surat.components.nomor_1}
					bind:mode={states.mode}
				/>
				<div>/</div>
				<FormField
					name="components.nomor_2"
					type="text"
					class="w-16 text-center"
					emptyDefault
					bind:value={states.surat.components.nomor_2}
					bind:mode={states.mode}
				/>
				<div>/</div>
				<FormField
					name="components.nomor_3"
					type="text"
					class="w-24 text-center"
					bind:value={states.surat.components.nomor_3}
					bind:mode={states.mode}
				/>
				<div>/</div>
				<FormField
					name="components.nomor_4"
					type="text"
					class="w-16 text-center"
					bind:value={states.surat.components.nomor_4}
					bind:mode={states.mode}
				/>
			</div>

			<div class="col-span-1 flex justify-between items-center">
				<span>Sifat</span>
				:
			</div>
			<div class="col-span-5">
				<FormField
					name="components.sifat"
					type="select"
					bind:value={states.surat.components.sifat}
					bind:mode={states.mode}
					selectOptions={DOCUMENT_IMPORTANCE}
				/>
			</div>

			<div class="col-span-1 flex justify-between items-center">
				<span>Lampiran</span>
				:
			</div>
			<div class="col-span-5">
				<FormField
					name="components.lampiran"
					type="number"
					bind:value={states.surat.components.lampiran}
					bind:mode={states.mode}
				/>
			</div>

			<div class="col-span-1 flex justify-between items-center">
				<span>Perihal</span>
				:
			</div>
			<div class="col-span-5">
				<FormField
					name="components.perihal"
					type="text"
					bind:value={states.surat.components.perihal}
					bind:mode={states.mode}
				/>
			</div>
		</div>
	{/if}
</form>

<style>
	:not(.issue) {
		font-family: 'Times New Roman', Times, serif;
	}
</style>
