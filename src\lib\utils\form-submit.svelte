<script lang="ts" generics="SuccessReturnType">
	import type {
		RemoteForm,
		RemoteFormInput,
		RemoteQuery,
		RemoteQueryOverride
	} from '@sveltejs/kit';
	import type { Snippet } from 'svelte';
	import type { DecodeFormValueErrors } from './validation';

	import * as AlertDialog from '$lib/components/ui/alert-dialog/index.js';
	import { getValidationErrorState } from './validation/validation-error-state.svelte';
	import { toast } from 'svelte-sonner';
	import { buttonVariants, type ButtonSize, type ButtonVariant } from '$lib/components/ui/button';
	import { cn } from '$lib/utils';
	import type { ClassValue } from 'svelte/elements';

	interface IProps {
		remoteForm: RemoteForm<
			RemoteFormInput,
			| { kind: 'decode_error'; errors: DecodeFormValueErrors }
			| { kind: 'success'; data: SuccessReturnType }
			| { kind: 'fetch' | 'json' | 'backend' | 'manual'; message: string }
		>;
		inputHiddens: Snippet;

		buttonText?: string | Snippet;
		pendingText?: string;

		variant?: ButtonVariant;
		size?: ButtonSize;
		class?: ClassValue;

		ifSuccess?: (data: SuccessReturnType) => void;
		ifError?: () => void;

		optimisticUpdates?: Array<RemoteQuery<any> | RemoteQueryOverride>;

		alertTitle?: string;
		alertDescription?: string;

		toastSuccess?: string;
		toastError?: string;
	}
	const {
		remoteForm,
		inputHiddens,

		buttonText = 'Submit',
		pendingText = 'Processing...',

		variant = 'default',
		size = 'lg',
		class: className,

		ifError = () => {},
		ifSuccess = () => {},
		optimisticUpdates = [],

		alertTitle = 'Apakah anda yakin?',
		alertDescription = 'Apakah anda yakin ingin melakukan aksi ini?',

		toastSuccess = 'Success',
		toastError
	}: IProps = $props();

	const validationState = getValidationErrorState();

	let formElement = $state<HTMLFormElement>();
	let alertDialogOpen = $state<boolean>(false);
</script>

<svelte:boundary>
	<form
		bind:this={formElement}
		{...remoteForm.enhance(async ({ submit }) => {
			await submit().updates(...optimisticUpdates);

			if (remoteForm.result?.kind === 'decode_error') {
				validationState.errors = remoteForm.result.errors;
				console.log(remoteForm.result.errors);
				toast.error('Please check your input.');
			} else validationState.errors = {};

			if (remoteForm.result?.kind === 'success') {
				toast.success(toastSuccess);
				ifSuccess(remoteForm.result.data);
			} else if (
				remoteForm.result?.kind === 'fetch' ||
				remoteForm.result?.kind === 'json' ||
				remoteForm.result?.kind === 'backend' ||
				remoteForm.result?.kind === 'manual'
			) {
				toast.error(toastError ?? remoteForm.result.message);
				ifError();
			}
		})}
		class="w-fit"
	>
		{@render inputHiddens()}
		<AlertDialog.Root bind:open={alertDialogOpen}>
			<AlertDialog.Trigger
				class={buttonVariants({
					variant,
					size,
					class: cn('cursor-pointer disabled:opacity-80', className)
				})}
				disabled={remoteForm.pending > 0}
				type="button"
			>
				{#if remoteForm.pending > 0}
					<svg
						class="mr-2 -ml-1 h-4 w-4 animate-spin"
						xmlns="http://www.w3.org/2000/svg"
						fill="none"
						viewBox="0 0 24 24"
					>
						<circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"
						></circle>
						<path
							class="opacity-75"
							fill="currentColor"
							d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
						></path>
					</svg>
					<span class="animate-pulse">{pendingText}</span>
				{:else if typeof buttonText === 'string'}
					<span>{buttonText}</span>
				{:else}
					{@render buttonText()}
				{/if}
			</AlertDialog.Trigger>

			<AlertDialog.Content>
				<AlertDialog.Header>
					<AlertDialog.Title>{alertTitle}</AlertDialog.Title>
					<AlertDialog.Description>
						{alertDescription}
					</AlertDialog.Description>
				</AlertDialog.Header>
				<AlertDialog.Footer>
					<AlertDialog.Cancel>Cancel</AlertDialog.Cancel>
					<AlertDialog.Action
						onclick={() => {
							formElement?.requestSubmit();
							alertDialogOpen = false;
						}}
					>
						Continue
					</AlertDialog.Action>
				</AlertDialog.Footer>
			</AlertDialog.Content>
		</AlertDialog.Root>
	</form>
</svelte:boundary>
