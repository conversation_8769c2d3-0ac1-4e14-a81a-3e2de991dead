<script lang="ts">
	import { _PersonilSurat } from '$lib/schema/surat';
	import { _Surat } from '$lib/schema/surat';
	import { getDocumentState, setDocumentState } from './components/document-state.svelte';

	import SuratTugasDewan from './components/surat-tugas-dewan.svelte';
	import SuratTugasSetwan from './components/surat-tugas-setwan.svelte';
	import NotaDinas from './components/nota-dinas.svelte';

	import DndMenu from './components/dnd-menu.svelte';

	setDocumentState();
	let states = $state(getDocumentState());

	const _dummy = [
		{
			..._PersonilSurat,
			anggota: {
				..._PersonilSurat.anggota,
				id: 1,
				nama: 'Eddie Lake',
				pangkat_golongan: { nama_pangkat: 'Pembina Tk. I', nama_golongan: 'IV.b', id: 1 },
				nip: '1234567890'
			},
			jabatan: {
				..._PersonilSurat.jabatan,
				id: 1,
				nama: 'Kepala'
			},
			alat_kelengkapan: {
				..._PersonilSurat.alat_kelengkapan,
				id: 1,
				nama: 'Umum',
				tipe_alat_kelengkapan: { id: 1, nama: 'Bagian' }
			}
		},
		{ ..._PersonilSurat, anggota: { ..._PersonilSurat.anggota, id: 3, nama: 'John Doe' } },
		{
			..._PersonilSurat,
			anggota: { ..._PersonilSurat.anggota, id: 2, nama: 'Jamik Tashpulatov' }
		},
		{ ..._PersonilSurat, anggota: { ..._PersonilSurat.anggota, id: 4, nama: 'Jane Doe' } }
	];

	$effect(() => {
		states = getDocumentState();

		if (
			states.surat.components.kind === 'Surat Tugas Setwan' ||
			states.surat.components.kind === 'Surat Tugas Dewan'
		)
			states.surat.components.kepada = _dummy;

		if (states.surat.components.kind === 'Nota Dinas') states.surat.components.peserta = _dummy;
	});
</script>

<DndMenu />

<div class="w-2/3 p-8 mx-auto bg-white shadow border border-muted">
	{#if states.surat.components.kind === 'Surat Tugas Dewan'}
		<SuratTugasDewan />
	{:else if states.surat.components.kind === 'Surat Tugas Setwan'}
		<SuratTugasSetwan />
	{:else if states.surat.components.kind === 'Nota Dinas'}
		<NotaDinas />
	{/if}
</div>
