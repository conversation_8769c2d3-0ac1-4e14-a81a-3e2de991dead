/* eslint-disable @typescript-eslint/no-empty-object-type */
import { Schema } from 'effect';
import { _Simple, AlatKelengkapanSchema, JabatanSchema, SimpleSchema } from './general';
import { _Personil, PersonilSchema } from './anggota';

export const KuotaSchema = Schema.Struct({
	id: Schema.Number,
	tipe_kuota: SimpleSchema,
	spesifik: Schema.NullOr(SimpleSchema),
	keterangan: Schema.String,
	value: Schema.Number
});

export interface Kuota extends Schema.Schema.Type<typeof KuotaSchema> {}
export interface KuotaEncoded extends Schema.Schema.Encoded<typeof KuotaSchema> {}

export const _Kuota = {
	id: 0,
	tipe_kuota: _Simple,
	spesifik: null,
	keterangan: '',
	value: 0
};

/////////////////////////////////////////////////////////////////////////////

export const KelompokKuotaSchema = Schema.Struct({
	kuota: KuotaSchema,
	tipe_anggota: SimpleSchema,
	alat_kelengkapan: Schema.NullOr(AlatKelengkapanSchema),
	jabatan: Schema.NullOr(JabatanSchema),
	value: Schema.Number
});

export interface KelompokKuota extends Schema.Schema.Type<typeof KelompokKuotaSchema> {}
export interface KelompokKuotaEncoded extends Schema.Schema.Encoded<typeof KelompokKuotaSchema> {}

export const _KelompokKuota: KelompokKuota = {
	kuota: _Kuota,
	tipe_anggota: _Simple,
	alat_kelengkapan: null,
	jabatan: null,
	value: 0
};

/////////////////////////////////////////////////////////////////////////////

export const IndividuKuotaSchema = Schema.Struct({
	kuota: KuotaSchema,
	personil: PersonilSchema,
	value: Schema.Number
});

export interface IndividuKuota extends Schema.Schema.Type<typeof IndividuKuotaSchema> {}
export interface IndividuKuotaEncoded extends Schema.Schema.Encoded<typeof IndividuKuotaSchema> {}

export const _IndividuKuota: IndividuKuota = {
	kuota: _Kuota,
	personil: _Personil,
	value: 0
};

/////////////////////////////////////////////////////////////////////////////
