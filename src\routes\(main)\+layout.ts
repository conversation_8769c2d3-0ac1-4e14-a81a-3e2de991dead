import type { LayoutLoad } from './$types';
import { capitalize } from 'effect/String';

const UNUSUAL_ROUTES: [string, string[]][] = [['', ['Dashboard']]];

export const load: LayoutLoad = async ({ url }) => {
	const routeSplitted = url.pathname
		.split('/')
		.filter(Boolean)
		.map((route) => capitalize(route));
	const routeJoined = routeSplitted.join('/');

	const unusual = UNUSUAL_ROUTES.find((route) => route[0] === routeJoined)?.[1];
	const breadcrumbs = unusual ?? routeSplitted;

	const unusualHref = UNUSUAL_ROUTES.find((route) => route[0] === routeJoined)?.[0];
	const breadcrumbsHref = unusualHref
		? [unusualHref]
		: breadcrumbs.map(
				(_, index) =>
					url.origin +
					'/' +
					breadcrumbs
						.map((route) => route.toLowerCase())
						.slice(0, index + 1)
						.join('/')
			);

	return {
		breadcrumbs,
		breadcrumbsHref
	};
};
