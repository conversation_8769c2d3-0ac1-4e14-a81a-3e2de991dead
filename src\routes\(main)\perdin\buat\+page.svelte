<script lang="ts">
	import * as PerdinRemote from '$lib/remote/perdin.remote';
	import FormSubmit from '$lib/utils/form-submit.svelte';

	import * as Tabs from '$lib/components/ui/tabs/index.js';
	import Separator from '$lib/components/ui/separator/separator.svelte';
	import { Button } from '$lib/components/ui/button/index.js';

	import BudgetField from './parts/budget-field.svelte';
	import GeneralField from './parts/general-field.svelte';
	import ParticipantsField from './parts/participants-field.svelte';

	import { setValidationErrorState } from '$lib/utils/validation/validation-error-state.svelte';
	import { getCreatePerdinState, setCreatePerdinState } from './states.svelte';
	import { goto } from '$app/navigation';

	setCreatePerdinState();
	const _state = getCreatePerdinState();

	setValidationErrorState();

	const tabs = ['general', 'participants', 'budget'];
	let tab = $state(tabs[0]);
	const index = $derived(tabs.indexOf(tab));

	$inspect(_state.perdin);
</script>

<div class="bg-white border flex-1 rounded-lg p-8 w-full lg:w-2/3 mx-auto">
	<h1 class="text-lg font-semibold leading-loose tracking-wide">Buat Perjalanan Dinas</h1>

	<Tabs.Root bind:value={tab} class="w-full">
		<Tabs.List>
			<Tabs.Trigger value="general">Informasi Umum</Tabs.Trigger>
			<Tabs.Trigger value="participants">Personil</Tabs.Trigger>
			<Tabs.Trigger value="budget">Alokasi Anggaran</Tabs.Trigger>
		</Tabs.List>

		<Separator class="my-2" />

		<Tabs.Content value="general">
			<GeneralField />
		</Tabs.Content>
		<Tabs.Content value="participants">
			<ParticipantsField />
		</Tabs.Content>
		<Tabs.Content value="budget">
			<BudgetField />
		</Tabs.Content>
	</Tabs.Root>

	<br />

	<div class="flex gap-4 items-center">
		<Button
			onclick={() => {
				if (index === -1) return;
				tab = tabs[index - 1];
			}}
			class={index === 0 ? 'hidden' : ''}
		>
			Sebelumnya
		</Button>

		<div class="flex-1"></div>

		{#if _state.perdin.id === 0}
			<FormSubmit
				remoteForm={PerdinRemote.draft}
				variant="default"
				class="font-semibold"
				size="default"
				alertTitle="Pemberitahuan Terkait Penyimpanan Informasi dalam Bentuk Draf"
				alertDescription="Sebelum anda dapat melanjutkan proses pembuatan, informasi yang telah anda masukkan akan disimpan sebagai draf. Informasi tersebut dapat anda sunting kembali sebelum anda menyelesaikan pembuatan perjalanan dinas ini. Apakah anda yakin ingin melanjutkan? "
				ifSuccess={(data) => {
					if (data) _state.perdin.id = data.id;
					tab = tabs[index + 1];
				}}
				toastSuccess="Draf Perjalanan Dinas berhasil dibuat."
			>
				{#snippet inputHiddens()}
					<input type="hidden" name="perdin" value={JSON.stringify(_state.perdin)} />
					<input type="hidden" name="kategori" value={JSON.stringify(_state.categories)} />
				{/snippet}

				{#snippet buttonText()}
					Selanjutnya
				{/snippet}
			</FormSubmit>
		{/if}

		{#if index === tabs.length - 1}
			<FormSubmit
				remoteForm={PerdinRemote.save}
				variant="default"
				class="font-semibold"
				size="default"
				alertTitle="Simpan dan Aktifkan Perdin"
				alertDescription="Anda akan menyimpan perdin ini dan mengaktifkannya. Perubahan selanjutnya dapat dilakukan di halaman detail perdin. Apakah anda yakin ingin melanjutkan?"
				toastSuccess="Perdin berhasil disimpan. Anda akan dialihkan ke halaman detail perdin..."
				ifSuccess={() => {
					goto(`/perdin/${_state.perdin.id}`);
				}}
			>
				{#snippet inputHiddens()}
					<input type="hidden" name="payload" value={JSON.stringify(_state.savePayload())} />
				{/snippet}

				{#snippet buttonText()}
					Simpan Perdin
				{/snippet}
			</FormSubmit>
		{/if}

		<Button
			onclick={() => {
				if (index === -1) return;
				tab = tabs[index + 1];
			}}
			class={index === tabs.length - 1 || _state.perdin.id === 0 ? 'hidden' : ''}
		>
			Selanjutnya
		</Button>
	</div>
</div>
