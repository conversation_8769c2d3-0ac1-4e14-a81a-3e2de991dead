<script lang="ts">
	import * as Sheet from '$lib/components/ui/sheet/index.js';
	import * as Avatar from '$lib/components/ui/avatar/index.js';
	import { Label } from '$lib/components/ui/label/index.js';
	import { Checkbox } from '$lib/components/ui/checkbox/index.js';
	import { buttonVariants } from '$lib/components/ui/button';
	import { Input } from '$lib/components/ui/input/index.js';
	import PlusIcon from '@tabler/icons-svelte/icons/plus';

	import { page } from '$app/state';

	import { getCreatePerdinState } from '../states.svelte';
	const states = getCreatePerdinState();
</script>

<Sheet.Root>
	<Sheet.Trigger class={buttonVariants({ variant: 'outline', size: 'sm', class: 'w-full my-4' })}>
		<PlusIcon /> Pilih Personil
	</Sheet.Trigger>

	<Sheet.Content>
		<Sheet.Header>
			<Sheet.Title>Pilih Personil DPRD</Sheet.Title>
			<Sheet.Description>
				Anggota yang ditampilkan adalah anggota DPRD yang aktif dalam periode ini.
			</Sheet.Description>
		</Sheet.Header>

		<Input type="search" placeholder="Cari personil..." />

		{#if page.data.dprdPersonnel}
			{#each Object.entries(page.data.dprdPersonnel) as [alat_kelengkapan, personnel]}
				<p class="text-stone-600 text-xs font-light px-2">Badan {alat_kelengkapan}</p>

				{#each personnel as p}
					{@const quotaFull = p.kuota.perjalanan.terpakai >= p.kuota.perjalanan.nilai}
					<Label
						class="flex items-center gap-4 p-2 px-4 border-b {quotaFull
							? 'text-muted-foreground opacity-75'
							: ''}"
					>
						<Checkbox
							id="dprd-{p.anggota.id}"
							class="data-[state=checked]:border-blue-600 data-[state=checked]:bg-blue-600 data-[state=checked]:text-white dark:data-[state=checked]:border-blue-700 dark:data-[state=checked]:bg-blue-700"
							checked={states.dprdPersonnel.some((personnel) => personnel.id === p.id)}
							onCheckedChange={(checked) => {
								if (checked) {
									states.dprdPersonnel.push(p);
								} else {
									states.dprdPersonnel = states.dprdPersonnel.filter(
										(personnel) => personnel.id !== p.id
									);
								}
							}}
							disabled={quotaFull}
						/>

						<Avatar.Root>
							<Avatar.Image src={p.anggota.path} alt="foto {p.anggota.nama}" />
							<Avatar.Fallback>{p.anggota.nama.slice(0, 2)}</Avatar.Fallback>
						</Avatar.Root>

						<div class="flex-1">
							<p class="text-stone-800 font-semibold">{p.anggota.nama}</p>
							<p class="font-light">{p.jabatan.nama}</p>
						</div>

						<div class="flex flex-col items-end">
							<div
								class="flex gap-2 items-center text-xs font-light tracking-wider"
								class:text-destructive={quotaFull}
							>
								Kuota :
								<span>{p.kuota.perjalanan.terpakai}</span> &sol;
								<span>{p.kuota.perjalanan.nilai}</span>
							</div>

							{#if quotaFull}
								<p class="text-xs text-destructive tracking-wider font-light">
									Kuota perjalanan sudah penuh.
								</p>
							{/if}
						</div>
					</Label>
				{/each}

				<br />
			{/each}
		{/if}
	</Sheet.Content>
</Sheet.Root>
