<script lang="ts">
	import { getCreatePerdinState } from '../states.svelte';
	import ParticipantsFieldSelectDPRD from './participants-field-select-dprd-sheet.svelte';
	import * as Avatar from '$lib/components/ui/avatar/index.js';
	import ParticipantsFieldSelectSetwanSheet from './participants-field-select-setwan-sheet.svelte';

	const states = getCreatePerdinState();
</script>

<h2 class="font-semibold">Langkah 2 : P<PERSON>ilihan <PERSON></h2>

<br />

<div class="grid grid-cols-1 gap-4">
	<div class="p-4 rounded border border-dashed border-accent-foreground">
		<div class="flex items-center">
			<h2 class="grow">DPRD</h2>
			<p class="text-sm text-muted-foreground">{states.dprdPersonnel.length} Personil</p>
		</div>

		<ParticipantsFieldSelectDPRD />

		<ol>
			{#each states.dprdPersonnel as p}
				<li class="flex gap-4 items-center py-2">
					<Avatar.Root>
						<Avatar.Image src={p.anggota.path} alt="foto {p.anggota.nama}" />
						<Avatar.Fallback>{p.anggota.nama.slice(0, 2)}</Avatar.Fallback>
					</Avatar.Root>

					<div class="flex-1">
						<p class="text-stone-800 font-semibold">{p.anggota.nama}</p>
						<p class="font-light text-sm">
							{p.jabatan.nama} Badan
							{p.alat_kelengkapan.nama}
						</p>
					</div>
				</li>
			{:else}
				<p class="text-muted-foreground text-center text-sm">Belum ada personil yang dipilih.</p>
				<p class="text-xs text-muted-foreground text-center">
					Klik 'Pilih Personil' untuk menambahkan
				</p>
			{/each}
		</ol>
	</div>
	<div class="p-4 rounded border border-dashed border-accent-foreground">
		<div class="flex items-center">
			<h2 class="grow">Sekretariat Dewan</h2>
			<p class="text-sm text-muted-foreground">{states.setwanPersonnel.length} Personil</p>
		</div>

		<ParticipantsFieldSelectSetwanSheet />

		<ol>
			{#each states.setwanPersonnel as p}
				<li class="flex gap-4 items-center py-2">
					<Avatar.Root>
						<Avatar.Image src={p.anggota.path} alt="foto {p.anggota.nama}" />
						<Avatar.Fallback>{p.anggota.nama.slice(0, 2)}</Avatar.Fallback>
					</Avatar.Root>

					<div class="flex-1">
						<p class="text-stone-800 font-semibold">{p.anggota.nama}</p>
						<p class="font-light text-sm">
							{p.jabatan.nama} Bagian
							{p.alat_kelengkapan.nama}
						</p>
					</div>
				</li>
			{:else}
				<p class="text-muted-foreground text-center text-sm">Belum ada personil yang dipilih.</p>
				<p class="text-xs text-muted-foreground text-center">
					Klik 'Pilih Personil' untuk menambahkan
				</p>
			{/each}
		</ol>
	</div>
</div>
