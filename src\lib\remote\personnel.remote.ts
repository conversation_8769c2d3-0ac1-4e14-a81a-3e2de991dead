import { query } from '$app/server';
import { _Personil, type Personil } from '$lib/schema/anggota';
import { effectfulFetch } from '$lib/utils/fetch';
import { Effect, Schema } from 'effect';
import type { Mutable } from 'effect/Types';
import {
	getTravelingGroupQuota,
	getTravelingIndividualQuota,
	type TravelingGroupQuota,
	type TravelingIndividualQuota
} from './quota.remote';

const quotaGroupCriteriaFilter = (
	userType: number,
	item: Personil,
	travelingQuota: TravelingGroupQuota[]
) => {
	let filtered = travelingQuota.filter(
		(quo) =>
			quo.tipe_anggota.id === userType &&
			(quo.alat_kelengkapan ? quo.alat_kelengkapan.id === item.alat_kelengkapan.id : true) &&
			(quo.jabatan ? quo.jabatan.id === item.jabatan.id : true)
	);

	return filtered;
};

const quotaIndividualCriteriaFilter = (
	item: Personil,
	travelingQuota: TravelingIndividualQuota[]
) => {
	return travelingQuota.filter((quo) => quo.personil.id === item.id);
};

export const getAllPersonnelByUserType = query(
	Schema.Number.pipe(Schema.standardSchemaV1),
	async (userType: number) => {
		const getter = effectfulFetch<Mutable<Personil[]>>(`/personil/tipe_anggota/${userType}`);
		const response = await Effect.runPromise(getter);

		if (response.kind !== 'success' || !response.data) return null;

		const travelingGroupQuota = await getTravelingGroupQuota();
		const travelingIndividualQuota = await getTravelingIndividualQuota();

		const addedQuota = response.data.map((item) => {
			let kuota = item.kuota ?? _Personil.kuota;

			kuota.perjalanan.nilai =
				quotaGroupCriteriaFilter(userType, item, travelingGroupQuota).length > 0
					? quotaGroupCriteriaFilter(userType, item, travelingGroupQuota)[0].kuota[0].value
					: kuota.perjalanan.nilai; // TODO : Haven't dealt with multiple quota (Specific Quota by Perdin Category)

			kuota.perjalanan.nilai =
				quotaIndividualCriteriaFilter(item, travelingIndividualQuota).length > 0
					? quotaIndividualCriteriaFilter(item, travelingIndividualQuota)[0].kuota[0].value
					: kuota.perjalanan.nilai;

			// TODO : Budget Quota

			return {
				...item,
				kuota: {
					...kuota,
					perjalanan: {
						...kuota.perjalanan,
						nilai: kuota.perjalanan.nilai
					}
				}, // TODO : Some javascript weirdness -_-
				path: `https://ui-avatars.com/api/?name=${item.anggota.nama}&background=000&color=fff&size=128` // TODO : Connect with backend photo getter API
			};
		});

		const grouped = addedQuota.reduce(
			(acc, item) => {
				acc[item.alat_kelengkapan.nama] ??= [];
				acc[item.alat_kelengkapan.nama].push(item);

				return acc;
			},
			{} as Record<string, Personil[]>
		);

		return grouped;
	}
);
