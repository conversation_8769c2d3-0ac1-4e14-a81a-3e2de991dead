import { browser } from '$app/environment';
import type { Simple } from '$lib/schema/general';
import type { KategoriPerdin, PerjalananDinas } from '$lib/schema/perdin';
import type { Surat } from '$lib/schema/surat';
import { DateTime } from 'effect';
import { getContext, setContext } from 'svelte';

export const PROGRESS_STATUS = ['Belum Dimulai', 'Sedang Berjalan', 'Sudah Terlaksana'] as const;
export type ProgressStatus = (typeof PROGRESS_STATUS)[number];

export interface CategoriesResponseType {
	id: number;
	kategori_perdin: KategoriPerdin;
	perdin: PerjalananDinas;
	assigned_at: string;
	assigned_by: number;
}

interface PerdinState {
	perdin: PerjalananDinas | null;
	categories: Record<string, CategoriesResponseType> | null;
	documents: Record<string, Surat[]> | null;

	mode: 'view' | 'edit';

	progressStatus: ProgressStatus;
	progressDay: [number, number];
}

export default class PerdinStateClass implements PerdinState {
	#perdin = $state<PerjalananDinas | null>(null);
	categories = $state<Record<number, CategoriesResponseType> | null>(null);
	documents = $state<Record<string, Surat[]> | null>(null);

	mode = $state<'view' | 'edit'>('view');

	progressStatus = $derived.by<ProgressStatus>(() => {
		if (!this.#perdin) return 'Belum Dimulai';

		const startDateUtc = DateTime.unsafeMake(this.#perdin.tanggal_mulai);
		const endDateUtc = DateTime.unsafeMake(this.#perdin.tanggal_selesai);
		const todayUtc = DateTime.unsafeMake(new Date().toISOString().split('T')[0]);

		if (DateTime.lessThan(todayUtc, startDateUtc)) return 'Belum Dimulai';
		if (DateTime.greaterThan(todayUtc, endDateUtc)) return 'Sudah Terlaksana';
		return 'Sedang Berjalan';
	});

	progressDay = $derived.by<[number, number]>(() => {
		if (this.progressStatus === 'Belum Dimulai' || !this.#perdin) return [0, 0];
		if (this.progressStatus === 'Sudah Terlaksana') return [100, 100];

		const startDateUtc = DateTime.unsafeMake(this.#perdin.tanggal_mulai);
		const endDateUtc = DateTime.unsafeMake(this.#perdin.tanggal_selesai);
		const todayUtc = DateTime.unsafeMake(new Date().toISOString().split('T')[0]);

		const totalDays =
			DateTime.distance(startDateUtc, endDateUtc.pipe(DateTime.add({ days: 1 }))) / 86400000; // Convert to days
		const passedDays =
			DateTime.distance(startDateUtc, todayUtc.pipe(DateTime.add({ days: 1 }))) / 86400000; // Convert to days

		return [passedDays, totalDays];
	});

	constructor(_perdin: PerjalananDinas | null) {
		this.#perdin = _perdin;
	}

	get perdin() {
		return this.#perdin;
	}

	set perdin(perdin: PerjalananDinas | null) {
		this.#perdin = perdin;
	}
}

const PERDIN_STATE_KEY = Symbol('@@perdin-state@@');

export function setPerdinState(): void {
	const perdin = browser ? localStorage.getItem('perdin') : null;
	if (perdin) {
		setContext(PERDIN_STATE_KEY, new PerdinStateClass(JSON.parse(perdin)));
	} else {
		setContext(PERDIN_STATE_KEY, new PerdinStateClass(null));
	}
}

export function getPerdinState(): PerdinState {
	return getContext<PerdinState>(PERDIN_STATE_KEY);
}
