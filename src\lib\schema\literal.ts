export const JENIS_KELAMIN = ['Laki-laki', '<PERSON><PERSON>pu<PERSON>'] as const;
export type Jen<PERSON>Kelamin = (typeof JENIS_KELAMIN)[number];

export const DB_STATUS = ['Active', 'Nonactive'] as const;
export type DBStatus = (typeof DB_STATUS)[number];

export const DB_ACTION = ['Add', 'Remove'] as const;
export type DBAction = (typeof DB_ACTION)[number];

export const PERDIN_STATUS = ['Draft', 'Active', 'Finished', 'Deleted'];
export type PerdinStatus = (typeof PERDIN_STATUS)[number];

export const DOCUMENT_STATUS = [
	'Menunggu Verifikasi',
	'Verifikasi Di-Setujui',
	'Verifikasi Di-Tolak',
	'Di-Hapus'
] as const;
export type DocumentStatus = (typeof DOCUMENT_STATUS)[number];

export const DOCUMENT_TYPE = [
	'Nota Dinas',
	'Surat Tugas Dewan',
	'Surat Tugas Setwan',
	'Surat Perjalanan Dinas'
] as const;
export type DocumentType = (typeof DOCUMENT_TYPE)[number];

export const DOCUMENT_IMPORTANCE = ['Penting', 'Biasa'] as const;
export type DocumentImportance = (typeof DOCUMENT_IMPORTANCE)[number];
