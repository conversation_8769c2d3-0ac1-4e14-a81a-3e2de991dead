<script lang="ts">
	import AsyncWrapper from '$lib/utils/async-wrapper.svelte';
	import DataTable from '$lib/components/data-table/data-table.svelte';
	import { columns } from './kegiatan-preview-table-columns.svelte';
	import { getAllPerdin } from '$lib/remote/perdin.remote';
</script>

<AsyncWrapper skeleton="table">
	<DataTable
		{columns}
		data={(await getAllPerdin({ start_date: '', end_date: '' })).data ?? []}
		options={{
			pagination: false,
			columnVisibility: false
		}}
	/>
</AsyncWrapper>
