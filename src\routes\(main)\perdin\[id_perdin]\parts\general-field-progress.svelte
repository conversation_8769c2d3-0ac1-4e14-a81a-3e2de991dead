<script lang="ts">
	import { getPerdinState } from '../states.svelte';
	import <PERSON>Field from '$lib/utils/form-field.svelte';
	import Progress from '$lib/components/ui/progress/progress.svelte';

	const state = getPerdinState();

	const progressColor = $derived.by(() => {
		if (state.progressStatus === 'Belum Dimulai') return 'bg-stone-300';
		if (state.progressStatus === 'Sedang Berjalan') return 'bg-amber-500';
		if (state.progressStatus === 'Sudah Terlaksana') return 'bg-emerald-500';
	});

	const progressTextColor = $derived.by(() => {
		if (state.progressStatus === 'Belum Dimulai') return 'text-stone-400';
		if (state.progressStatus === 'Sedang Berjalan') return 'text-amber-700';
		if (state.progressStatus === 'Sudah Terlaksana') return 'text-emerald-700';
	});
</script>

{#if state.perdin}
	<div class="flex gap-8 items-center justify-between">
		<FormField
			name="tanggal_mulai"
			label="Tanggal Mulai"
			labelClass="text-xs text-center text-nowrap leading-loose"
			type="date"
			value={state.perdin.tanggal_mulai}
			mode={state.mode}
			max={state.perdin.tanggal_selesai}
			viewClass="text-center"
		/>

		<div class="flex flex-col gap-1 items-center w-full">
			<p class="text-xs tracking-wide {progressTextColor}">
				{state.progressStatus}
				{#if state.progressStatus === 'Sedang Berjalan'}
					<span class="opacity-50">
						(Hari ke-{state.progressDay[0]} dari {state.progressDay[1]})
					</span>
				{/if}
			</p>
			<Progress
				value={state.progressDay[0]}
				max={state.progressDay[1]}
				childClass="w-full {progressColor}"
			/>
		</div>

		<FormField
			name="tanggal_selesai"
			label="Tanggal Selesai"
			labelClass="text-xs text-center text-nowrap leading-loose"
			type="date"
			value={state.perdin.tanggal_selesai}
			mode={state.mode}
			viewClass="text-center"
		/>
	</div>
{/if}
