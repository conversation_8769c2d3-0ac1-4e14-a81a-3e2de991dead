import { getAllPersonnelByUserType } from '$lib/remote/personnel.remote';
import type { PageServerLoad } from './$types';
import * as BudgetRemote from '$lib/remote/budget.remote';

export const load: PageServerLoad = async () => {
	const dprdPersonnel = await getAllPersonnelByUserType(1);
	const setwanPersonnel = await getAllPersonnelByUserType(2);

	const annualBudgetWithLogInfo = await BudgetRemote.annualWithLogInfo();
	const activityBudget = await BudgetRemote.activity();

	return { dprdPersonnel, setwanPersonnel, annualBudgetWithLogInfo, activityBudget };
};
