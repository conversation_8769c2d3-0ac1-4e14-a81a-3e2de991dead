import { env } from '$env/dynamic/private';
import type { HandleFetch } from '@sveltejs/kit';

export const handleFetch: HandleFetch = async ({ fetch, request, event }) => {
	if (request.url.startsWith(env.API_HOST)) {
		if (request.headers.get('send-mode') !== 'form-data')
			request.headers.set('Content-Type', 'application/json');

		request.headers.set('Accept', '*/*');
		request.headers.set('Connection', 'keep-alive');
		request.headers.set('Accept-Encoding', 'gzip, deflate, br');

		if (event.locals.auth && request.url.includes('/login') === false) {
			request.headers.set('Authorization', `Bearer ${event.locals.auth.jwt_token}`);
		}
	}

	return fetch(request);
};
