/* eslint-disable @typescript-eslint/no-empty-object-type */

import { Schema } from 'effect';
import { DB_STATUS } from './literal';

export const SimpleSchema = Schema.Struct({
	id: Schema.NumberFromString,
	nama: Schema.NonEmptyString
});

export interface Simple extends Schema.Schema.Type<typeof SimpleSchema> {}
export interface SimpleEncoded extends Schema.Schema.Encoded<typeof SimpleSchema> {}

export const _Simple: Simple = {
	id: 0,
	nama: ''
};

/////////////////////////////////////////////////////////////////////////////

export const UpdatedSchema = Schema.Struct({
	updated_at: Schema.Date,
	updated_by: Schema.Number,
	status: Schema.Literal(...DB_STATUS)
});

export interface Updated extends Schema.Schema.Type<typeof UpdatedSchema> {}
export interface UpdatedEncoded extends Schema.Schema.Encoded<typeof UpdatedSchema> {}

export const _Updated: Updated = {
	updated_at: new Date(),
	updated_by: 0,
	status: 'Active'
};

/////////////////////////////////////////////////////////////////////////////

export const AssignedSchema = Schema.Struct({
	assigned_at: Schema.Date,
	assigned_by: Schema.Number
});

export interface Assigned extends Schema.Schema.Type<typeof AssignedSchema> {}
export interface AssignedEncoded extends Schema.Schema.Encoded<typeof AssignedSchema> {}

export const _Assigned: Assigned = {
	assigned_at: new Date(),
	assigned_by: 0
};

/////////////////////////////////////////////////////////////////////////////

export const CreatedSchema = Schema.Struct({
	created_at: Schema.DateFromString,
	created_by: Schema.NumberFromString
});

export interface Created extends Schema.Schema.Type<typeof CreatedSchema> {}
export interface CreatedEncoded extends Schema.Schema.Encoded<typeof CreatedSchema> {}

export const _Created: Created = {
	created_at: new Date(),
	created_by: 0
};

/////////////////////////////////////////////////////////////////////////////

export const PangkatGolonganSchema = Schema.Struct({
	id: Schema.NumberFromString,
	nama_pangkat: Schema.NonEmptyString,
	nama_golongan: Schema.NonEmptyString
});

export interface PangkatGolongan extends Schema.Schema.Type<typeof PangkatGolonganSchema> {}
export interface PangkatGolonganEncoded
	extends Schema.Schema.Encoded<typeof PangkatGolonganSchema> {}

export const _PangkatGolongan: PangkatGolongan = {
	id: 0,
	nama_pangkat: '',
	nama_golongan: ''
};

/////////////////////////////////////////////////////////////////////////////

export const AlatKelengkapanSchema = Schema.Struct({
	id: Schema.NumberFromString,
	nama: Schema.NonEmptyString,
	tipe_alat_kelengkapan: SimpleSchema
});

export interface AlatKelengkapan extends Schema.Schema.Type<typeof AlatKelengkapanSchema> {}
export interface AlatKelengkapanEncoded
	extends Schema.Schema.Encoded<typeof AlatKelengkapanSchema> {}

export const _AlatKelengkapan: AlatKelengkapan = {
	id: 0,
	nama: '',
	tipe_alat_kelengkapan: _Simple
};

/////////////////////////////////////////////////////////////////////////////

export const JabatanSchema = Schema.Struct({
	id: Schema.Number,
	nama: Schema.NonEmptyString,
	hierarki: Schema.Number,
	tipe_alat_kelengkapan: SimpleSchema
});

export interface Jabatan extends Schema.Schema.Type<typeof JabatanSchema> {}
export interface JabatanEncoded extends Schema.Schema.Encoded<typeof JabatanSchema> {}

export const _Jabatan: Jabatan = {
	id: 0,
	nama: '',
	hierarki: 0,
	tipe_alat_kelengkapan: _Simple
};

/////////////////////////////////////////////////////////////////////////////
