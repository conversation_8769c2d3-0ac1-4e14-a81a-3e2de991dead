<script lang="ts">
	import SquarePen from '@lucide/svelte/icons/square-pen';

	import { Button } from '$lib/components/ui/button/index.js';
	import { Separator } from '$lib/components/ui/separator/index.js';

	import KegiatanPreviewTable from './kegiatan-preview-table.svelte';
</script>

<div class="flex gap-4 items-center">
	<div class="flex flex-col bg-emerald-400 w-fit items-center p-2 rounded shadow">
		<h1 class="font-bold tabular-nums text-4xl w-fit tracking-wide text-muted">14</h1>
		<h2 class="font-bold uppercase text-xl text-muted tracking-tight">AUG</h2>
	</div>

	<div class="grow">
		<p class="text-muted-foreground text-center">3 Kegiatan</p>
	</div>

	<div>
		<p class="text-muted-foreground text-center mb-1 text-xs">Ajukan <PERSON></p>
		<Button
			variant="outline"
			class="whitespace-normal w-12 h-fit min-w-fit text-wrap uppercase font-black text-xs tracking-wide"
			href="/perdin/buat"
		>
			<SquarePen class="w-4 h-4" />
			Buat Perjalanan Dinas
		</Button>
	</div>
</div>

<Separator class="my-4" />

<KegiatanPreviewTable />
