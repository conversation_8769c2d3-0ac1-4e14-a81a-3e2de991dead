import { JENIS_KELAMIN } from './literal';

import { Schema } from 'effect';

import {
	_AlatKelengkapan,
	_Assigned,
	_PangkatGolongan,
	_Simple,
	AlatKelengkapanSchema,
	AssignedSchema,
	PangkatGolonganSchema,
	SimpleSchema
} from './general';
import { EmailSchema, OnlyNumberStringSchema, PhoneNumberSchema } from './utility';

export const AnggotaSchema = Schema.Struct({
	id: Schema.NumberFromString,
	nama: Schema.NonEmptyString,
	nip: OnlyNumberStringSchema,

	tanggal_lahir: Schema.DateFromString,
	nomor_telepon: PhoneNumberSchema,
	email: EmailSchema,
	alamat: Schema.NonEmptyString,
	jenis_kelamin: Schema.Literal(...JENIS_KELAMIN),

	username: Schema.NonEmptyString.pipe(Schema.minLength(4)),
	password: Schema.NonEmptyString.pipe(Schema.minLength(4)),

	status_anggota: SimpleSchema,
	tipe_anggota: SimpleSchema,

	fraksi: SimpleSchema,
	pangkat_golongan: PangkatGolonganSchema,

	path: Schema.String
});

export interface Anggota extends Schema.Schema.Type<typeof AnggotaSchema> {}
export interface AnggotaEncoded extends Schema.Schema.Encoded<typeof AnggotaSchema> {}

export const _Anggota: Anggota = {
	id: 0,
	nama: '',
	nip: '',

	tanggal_lahir: new Date(),
	nomor_telepon: '',
	email: '',
	alamat: '',
	jenis_kelamin: 'Laki-laki',

	username: '', // TEMPORARY
	password: '', // TEMPORARY

	status_anggota: _Simple,
	tipe_anggota: _Simple,

	fraksi: _Simple,
	pangkat_golongan: _PangkatGolongan,

	path: ''
};

/////////////////////////////////////////////////////////////////////////////

export const PersonilSchema = Schema.Struct({
	id: Schema.NumberFromString,

	alat_kelengkapan: AlatKelengkapanSchema,
	jabatan: SimpleSchema,

	awal_periode: Schema.DateFromString,
	akhir_periode: Schema.DateFromString,

	anggota: AnggotaSchema,

	kuota: Schema.Struct({
		perjalanan: Schema.Struct({
			nilai: Schema.NumberFromString,
			terpakai: Schema.NumberFromString
		}).pipe(Schema.mutable),
		anggaran: Schema.Struct({
			nilai: Schema.NumberFromString,
			terpakai: Schema.NumberFromString
		}).pipe(Schema.mutable)
	})
});

export interface Personil extends Schema.Schema.Type<typeof PersonilSchema> {}
export interface PersonilEncoded extends Schema.Schema.Encoded<typeof PersonilSchema> {}

export const _Personil: Personil = {
	id: 0,

	alat_kelengkapan: _AlatKelengkapan,
	jabatan: _Simple,

	awal_periode: new Date(),
	akhir_periode: new Date(),

	anggota: _Anggota,

	kuota: {
		perjalanan: {
			nilai: 0,
			terpakai: 0
		},
		anggaran: {
			nilai: 0,
			terpakai: 0
		}
	}
};

/////////////////////////////////////////////////////////////////////////////

export const StatusAnggotaSchema = Schema.Struct({
	id: Schema.NumberFromString,
	id_anggota: Schema.NumberFromString,

	keterangan: Schema.String,
	status_anggota: SimpleSchema,

	...AssignedSchema.fields
});

export interface StatusAnggota extends Schema.Schema.Type<typeof StatusAnggotaSchema> {}
export interface StatusAnggotaEncoded extends Schema.Schema.Encoded<typeof StatusAnggotaSchema> {}

export const _StatusAnggota: StatusAnggota = {
	id: 0,
	id_anggota: 0,

	keterangan: '',
	status_anggota: _Simple,

	..._Assigned
};
