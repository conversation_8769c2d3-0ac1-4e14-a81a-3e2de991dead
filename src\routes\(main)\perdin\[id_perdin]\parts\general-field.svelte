<script lang="ts">
	import GeneralFieldDocuments from './general-field-documents.svelte';
	import GeneralFieldInfo from './general-field-info.svelte';
	import GeneralFieldProgress from './general-field-progress.svelte';
	import GeneralFieldStatistics from './general-field-statistics.svelte';
	import Separator from '$lib/components/ui/separator/separator.svelte';
	import { setValidationErrorState } from '$lib/utils/validation/validation-error-state.svelte';
	import GeneralFieldControl from './general-field-control.svelte';
	import AsyncWrapper from '$lib/utils/async-wrapper.svelte';
	import GeneralFieldCategories from './general-field-categories.svelte';
	import GeneralFieldMetadata from './general-field-metadata.svelte';

	setValidationErrorState();
</script>

<div class="grid grid-cols-4 gap-6">
	<div class="col-span-2 rounded-lg bg-white p-8 border">
		<GeneralFieldInfo />
	</div>

	<div class="col-span-2 rounded-lg bg-white p-8 border">
		<!-- <GeneralFieldControl /> -->
		<GeneralFieldMetadata />
	</div>

	<div class="col-span-4 px-8 py-4">
		<GeneralFieldProgress />
	</div>

	<Separator class=" col-span-4" />

	<div class="col-span-4 p-8 border bg-white rounded-lg">
		<AsyncWrapper skeleton="list">
			<GeneralFieldCategories />
		</AsyncWrapper>
	</div>

	<Separator class="shadow-2xl col-span-4" />

	<div class="col-span-4">
		<AsyncWrapper skeleton="blocks">
			<GeneralFieldDocuments />
		</AsyncWrapper>
	</div>
	<div class="col-span-4">
		<GeneralFieldStatistics />
	</div>
</div>
