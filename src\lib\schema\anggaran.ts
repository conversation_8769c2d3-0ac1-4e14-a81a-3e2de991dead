/* eslint-disable @typescript-eslint/no-empty-object-type */
import { Schema } from 'effect';
import { _Assigned, _Simple, AssignedSchema, SimpleSchema } from './general';
import { _PerjalananDinas, PerjalananDinasSchema } from './perdin';

export const AnggaranTahunanSchema = Schema.Struct({
	id: Schema.Number,
	nama: Schema.NonEmptyString,
	kode_rekening: Schema.NonEmptyString
});

export interface AnggaranTahunan extends Schema.Schema.Type<typeof AnggaranTahunanSchema> {}
export interface AnggaranTahunanEncoded
	extends Schema.Schema.Encoded<typeof AnggaranTahunanSchema> {}

export const _AnggaranTahunan: AnggaranTahunan = {
	id: 0,
	nama: '',
	kode_rekening: ''
};

/////////////////////////////////////////////////////////////////////////////

export const AnggaranKegiatanSchema = Schema.Struct({
	id: Schema.Number,
	nama: Schema.NonEmptyString,
	kode_rekening: Schema.NonEmptyString,
	anggaran_tahunan: AnggaranTahunanSchema
});

export interface AnggaranKegiatan extends Schema.Schema.Type<typeof AnggaranKegiatanSchema> {}
export interface AnggaranKegiatanEncoded
	extends Schema.Schema.Encoded<typeof AnggaranKegiatanSchema> {}

export const _AnggaranKegiatan: AnggaranKegiatan = {
	id: 0,
	nama: '',
	kode_rekening: '',
	anggaran_tahunan: _AnggaranTahunan
};

/////////////////////////////////////////////////////////////////////////////

export const AnggaranTahunanLogSchema = Schema.Struct({
	id: Schema.Number,
	anggaran_tahunan: AnggaranTahunanSchema,
	aksi_finansial: SimpleSchema,

	tahun: Schema.Number,
	nilai: Schema.Number,
	keterangan: Schema.String,

	saldo: Schema.Number,

	...AssignedSchema.fields
});

export interface AnggaranTahunanLog extends Schema.Schema.Type<typeof AnggaranTahunanLogSchema> {}
export interface AnggaranTahunanLogEncoded
	extends Schema.Schema.Encoded<typeof AnggaranTahunanLogSchema> {}

export const _AnggaranTahunanLog: AnggaranTahunanLog = {
	id: 0,
	anggaran_tahunan: _AnggaranTahunan,
	aksi_finansial: _Simple,
	tahun: 0,
	nilai: 0,
	keterangan: '',
	saldo: 0,
	..._Assigned
};

/////////////////////////////////////////////////////////////////////////////

export const AnggaranKegiatanLog = Schema.Struct({
	id: Schema.Number,
	anggaran_kegiatan: AnggaranKegiatanSchema,
	perjalanan_dinas: PerjalananDinasSchema,

	nilai: Schema.Number,
	keterangan: Schema.String,

	...AssignedSchema.fields
});

export interface AnggaranKegiatanLog extends Schema.Schema.Type<typeof AnggaranKegiatanLog> {}
export interface AnggaranKegiatanLogEncoded
	extends Schema.Schema.Encoded<typeof AnggaranKegiatanLog> {}

export const _AnggaranKegiatanLog: AnggaranKegiatanLog = {
	id: 0,
	anggaran_kegiatan: _AnggaranKegiatan,
	perjalanan_dinas: _PerjalananDinas,
	nilai: 0,
	keterangan: '',
	..._Assigned
};

/////////////////////////////////////////////////////////////////////////////

export const AnggaranTahunanLogInfoSchema = Schema.Struct({
	...AnggaranTahunanSchema.fields,

	total: Schema.Positive,
	terpakai: Schema.Positive,
	tersedia: Schema.Positive
});

export interface AnggaranTahunanLogInfo
	extends Schema.Schema.Type<typeof AnggaranTahunanLogInfoSchema> {}
export interface AnggaranTahunanLogInfoEncoded
	extends Schema.Schema.Encoded<typeof AnggaranTahunanLogInfoSchema> {}

export const _AnggaranTahunanLogInfo: AnggaranTahunanLogInfo = {
	..._AnggaranTahunan,
	total: 0,
	terpakai: 0,
	tersedia: 0
};
