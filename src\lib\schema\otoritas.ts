/* eslint-disable @typescript-eslint/no-empty-object-type */
import { Schema } from 'effect';
import {
	_AlatKelengkapan,
	_Simple,
	_Updated,
	AlatKelengkapanSchema,
	SimpleSchema,
	UpdatedSchema
} from './general';

import { DB_ACTION } from './literal';
import { _Anggota, AnggotaSchema } from './anggota';

export const OtoritasSchema = Schema.Struct({
	id: Schema.Number,
	aksi_otoritas: SimpleSchema,
	tipe_entitas: SimpleSchema,
	keterangan: Schema.String,

	otoritas: Schema.NullishOr(Schema.String)
});

export interface Otoritas extends Schema.Schema.Type<typeof OtoritasSchema> {}
export interface OtoritasEncoded extends Schema.Schema.Encoded<typeof OtoritasSchema> {}

export const _Otoritas: Otoritas = {
	id: 0,
	aksi_otoritas: _Simple,
	tipe_entitas: _Simple,
	keterangan: '',
	otoritas: null
};

/////////////////////////////////////////////////////////////////////////////

export const SimpleOtoritasSchema = Schema.Struct({
	id: Schema.Number,
	id_helper: Schema.Number,
	nama_aksi_otoritas: Schema.String,
	nama_tipe_entitas: Schema.String,
	action: Schema.Literal(...DB_ACTION)
});

export interface SimpleOtoritas extends Schema.Schema.Type<typeof SimpleOtoritasSchema> {}
export interface SimpleOtoritasEncoded extends Schema.Schema.Encoded<typeof SimpleOtoritasSchema> {}

export const _SimpleOtoritas: SimpleOtoritas = {
	id: 0,
	id_helper: 0,
	nama_aksi_otoritas: '',
	nama_tipe_entitas: '',
	action: 'Add'
};

/////////////////////////////////////////////////////////////////////////////

export const KelompokOtoritasSchema = Schema.Struct({
	id: Schema.Number,

	tipe_anggota: SimpleSchema,
	alat_kelengkapan: AlatKelengkapanSchema,
	jabatan: SimpleSchema,

	otoritas: Schema.Array(SimpleOtoritasSchema),

	...UpdatedSchema.fields
});

export interface KelompokOtoritas extends Schema.Schema.Type<typeof KelompokOtoritasSchema> {}
export interface KelompokOtoritasEncoded
	extends Schema.Schema.Encoded<typeof KelompokOtoritasSchema> {}

export const _KelompokOtoritas: KelompokOtoritas = {
	id: 0,
	tipe_anggota: _Simple,
	alat_kelengkapan: _AlatKelengkapan,
	jabatan: _Simple,
	otoritas: [_SimpleOtoritas],
	..._Updated
};

//

export const IndividuOtoritasSchema = Schema.Struct({
	id: Schema.Number,
	anggota: AnggotaSchema,

	otoritas: Schema.Array(SimpleOtoritasSchema),

	action: Schema.Literal(...DB_ACTION),

	...UpdatedSchema.fields
});

export interface IndividuOtoritas extends Schema.Schema.Type<typeof IndividuOtoritasSchema> {}
export interface IndividuOtoritasEncoded
	extends Schema.Schema.Encoded<typeof IndividuOtoritasSchema> {}

export const _IndividuOtoritas: IndividuOtoritas = {
	id: 0,
	anggota: _Anggota,
	otoritas: [_SimpleOtoritas],
	action: 'Add',
	..._Updated
};
