<script lang="ts">
	import FormField from '$lib/utils/form-field.svelte';
	import { getPerdinState } from '../states.svelte';
	import Progress from '$lib/components/ui/progress/progress.svelte';
	import PerdinStatus from '../components/perdin-status.svelte';
	import { Button } from '$lib/components/ui/button/index.js';
	import Separator from '$lib/components/ui/separator/separator.svelte';
	import FormSubmit from '$lib/utils/form-submit.svelte';

	import Pencil from '@tabler/icons-svelte/icons/pencil';
	import Save from '@lucide/svelte/icons/save';
	import { deletePerdin } from '$lib/remote/perdin.remote';
	import Trash from '@tabler/icons-svelte/icons/trash';

	const state = getPerdinState();
</script>

{#if state.perdin}
	<div class="flex justify-end items-center gap-4">
		<PerdinStatus status={state.perdin.status_perdin.nama} size="m" />

		{#if state.mode === 'view'}
			<Button
				size="lg"
				class="font-semibold bg-blue-500"
				onclick={() => {
					state.mode = 'edit';
				}}
			>
				<Pencil /> Sunting Informasi Perdin
			</Button>
		{/if}

		{#if state.mode === 'edit'}
			<Button
				size="lg"
				class="font-semibold bg-green-500"
				onclick={() => {
					state.mode = 'view';
				}}
			>
				<Save /> Simpan Informasi Perdin
			</Button>
		{/if}

		<FormSubmit remoteForm={deletePerdin} variant="destructive" class="font-semibold">
			{#snippet inputHiddens()}
				<input type="hidden" name="id" value={state.perdin?.id} />
			{/snippet}

			{#snippet buttonText()}
				<Trash />
				Hapus Permanen
			{/snippet}
		</FormSubmit>
	</div>
{/if}
