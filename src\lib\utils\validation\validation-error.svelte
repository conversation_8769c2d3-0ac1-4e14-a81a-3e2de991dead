<script lang="ts">
	import { getValidationErrorState } from './validation-error-state.svelte';
	import CircleAlertIcon from '@lucide/svelte/icons/circle-alert';

	interface IProps {
		name: string;
	}

	const { name }: IProps = $props();

	const validationErrorState = getValidationErrorState();
</script>

<div class="mt-1">
	{#if !validationErrorState}
		<div class="text-xs text-destructive/40">Validation Error State is Not Set.</div>
	{:else if validationErrorState.errors && validationErrorState.errors[name]}
		<ul class="validation-error py-2 text-xs text-destructive">
			{#each validationErrorState.errors[name] as error, i (error._tag + i)}
				<li class="flex gap-2 items-center"><CircleAlertIcon size="14" /> {error.message}</li>
			{/each}
		</ul>
	{/if}
</div>
