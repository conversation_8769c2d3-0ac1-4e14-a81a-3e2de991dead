<script lang="ts">
	import type { PerdinStatus } from '$lib/schema/literal';

	interface IProps {
		status: PerdinStatus;
		size: 's' | 'm' | 'l';
	}

	const { status = 'Draft', size = 'm' }: IProps = $props();

	const sizeNumber = {
		s: 'h-6 text-xs',
		m: 'h-8 text-sm',
		l: 'h-10 text-base'
	};

	const statusColor = {
		Draft: 'bg-stone-200 text-stone-700',
		Active: 'bg-amber-400 text-white',
		Finished: 'bg-emerald-400 text-white',
		Deleted: 'bg-rose-500 text-white'
	};
</script>

<div
	class="rounded-full w-fit px-3 grid place-items-center tracking-wide {sizeNumber[
		size as keyof typeof sizeNumber
	]} {statusColor?.[status as keyof typeof statusColor]}"
>
	{status}
</div>
