<script lang="ts">
	import Separator from '$lib/components/ui/separator/separator.svelte';
	import PerdinStatus from '../components/perdin-status.svelte';

	import { getPerdinState } from '../states.svelte';
	import { formatter } from '../../buat/states.svelte';

	const state = getPerdinState();
</script>

<div class="flex flex-col gap-2 text-xs">
	{#if state.perdin}
		<div class="grid gap-4 grid-cols-12 items-center">
			<p class="text-muted-foreground tracking-wide col-span-5">Status Perjalanan Dinas</p>
			<p class="col-span-1">:</p>
			<PerdinStatus status={state.perdin.status_perdin.nama} size="s" />
		</div>
	{/if}

	<Separator />

	<div class="grid gap-4 grid-cols-12 items-center">
		<p class="text-muted-foreground tracking-wide col-span-5">Jumlah Personil DPRD</p>
		<p class="col-span-1">:</p>
		<p class="">{0}</p>
	</div>

	<div class="grid gap-4 grid-cols-12 items-center">
		<p class="text-muted-foreground tracking-wide col-span-5">Jumlah Personil Setwan</p>
		<p class="col-span-1">:</p>
		<p class="">{0}</p>
	</div>

	<div class="grid gap-4 grid-cols-12 items-center">
		<p class="text-muted-foreground tracking-wide col-span-5">Total Anggaran</p>
		<p class="col-span-1">:</p>
		<p class="tracking-wider">{formatter.format(0)}</p>
	</div>

	<Separator />

	<div class="grid gap-4 grid-cols-12 items-center">
		<p class="text-muted-foreground tracking-wide col-span-5">Jumlah Surat Ter-verifikasi</p>
		<p class="col-span-1">:</p>
		<p class="">{0}</p>
	</div>

	<div class="grid gap-4 grid-cols-12 items-center">
		<p class="text-muted-foreground tracking-wide col-span-5 leading-tight">
			Jumlah Surat Membutuhkan Verifikasi
		</p>
		<p class="col-span-1">:</p>
		<p class="">{0}</p>
	</div>
</div>
