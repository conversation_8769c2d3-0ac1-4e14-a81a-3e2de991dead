<script lang="ts">
	import FormField from '$lib/utils/form-field.svelte';
	import { getCreatePerdinState } from '../states.svelte';

	const state = getCreatePerdinState();

	import { RangeCalendar } from '$lib/components/ui/range-calendar/index.js';
	import { Label } from '$lib/components/ui/label';
	import { DateFormatter } from '@internationalized/date';

	import GeneralFieldCategories from './general-field-categories.svelte';
	import AsyncWrapper from '$lib/utils/async-wrapper.svelte';
	import ValidationError from '$lib/utils/validation/validation-error.svelte';

	const dateFormatter = new DateFormatter('id-ID', {
		dateStyle: 'long',
		timeZone: 'Asia/Jakarta'
	});
</script>

<h2 class="font-semibold">Langkah 1: Informasi Umum</h2>

<FormField
	name="nama"
	type="text"
	label="Kegiatan Perjalanan Dinas"
	placeholder="Nama / Judul Kegiatan"
	bind:value={state.perdin.nama}
	labelClass="text-stone-900 leading-10"
/>

<FormField
	type="textarea"
	name="deskripsi"
	label="Deskripsi"
	placeholder="Deskripsi Singkat Kegiatan"
	bind:value={state.perdin.deskripsi}
	labelClass="text-stone-900 leading-10"
	rows={5}
/>

<div class="flex gap-4 items-center flex-col md:flex-row">
	<div class="grow flex-0">
		<Label for="tanggal" class="block text-stone-900 tracking-wide leading-10">
			Tanggal Pelaksanaan
		</Label>

		<RangeCalendar
			locale="id-ID"
			bind:value={state.tanggal}
			captionLayout="dropdown"
			numberOfMonths={2}
			class="w-fit bg-transparent"
		/>
	</div>

	<div class="shrink-0 grow">
		<p class="text-xs text-muted-foreground tracking-wide leading-8">Tanggal Mulai</p>
		<p class="font-semibold text-sm">
			{state.tanggal.start
				? dateFormatter.format(state.tanggal.start.toDate('Asia/Jakarta'))
				: `Belum dipilih.`}
		</p>
		<ValidationError name="tanggal_mulai" />

		<p class="text-xs text-muted-foreground tracking-wide leading-8">Tanggal Selesai</p>
		<p class="font-semibold text-sm">
			{state.tanggal.end
				? dateFormatter.format(state.tanggal.end.toDate('Asia/Jakarta'))
				: `Belum dipilih.`}
		</p>
		<ValidationError name="tanggal_selesai" />

		<p class="text-xs text-muted-foreground tracking-wide leading-8">Durasi Pelaksanaan</p>
		<p class="font-semibold text-sm">
			{state.tanggal.start && state.tanggal.end
				? Math.abs(
						state.tanggal.end.toDate('Asia/Jakarta').getTime() -
							state.tanggal.start.toDate('Asia/Jakarta').getTime()
					) /
						(1000 * 60 * 60 * 24) +
					1 +
					' Hari'
				: `Belum dipilih.`}
		</p>
	</div>
</div>

<AsyncWrapper skeleton="blocks">
	<GeneralFieldCategories />
</AsyncWrapper>
