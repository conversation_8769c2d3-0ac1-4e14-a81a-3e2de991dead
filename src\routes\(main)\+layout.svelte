<script lang="ts">
	import { page } from '$app/state';
	import AppSidebar from '$lib/components/app-sidebar.svelte';
	import * as Breadcrumb from '$lib/components/ui/breadcrumb/index.js';
	import { Separator } from '$lib/components/ui/separator/index.js';
	import * as Sidebar from '$lib/components/ui/sidebar/index.js';

	const { children } = $props();
</script>

<Sidebar.Provider>
	<AppSidebar />
	<Sidebar.Inset>
		<header
			class="group-has-data-[collapsible=icon]/sidebar-wrapper:h-12 flex h-16 shrink-0 items-center gap-2 transition-[width,height] ease-linear"
		>
			<div class="flex items-center gap-2 px-4">
				<Sidebar.Trigger class="-ml-1" />
				<Separator orientation="vertical" class="mr-2 data-[orientation=vertical]:h-4" />
				<Breadcrumb.Root>
					<Breadcrumb.List>
						{#if page.data.breadcrumbs}
							{#each page.data.breadcrumbs as breadcrumb, index (breadcrumb + index)}
								<Breadcrumb.Item class="hidden md:block">
									<Breadcrumb.Link
										href={page.data.breadcrumbsHref?.[index]}
										class={index === page.data.breadcrumbs?.length - 1 ? 'text-primary' : ''}
									>
										{breadcrumb}
									</Breadcrumb.Link>
								</Breadcrumb.Item>

								<Breadcrumb.Separator class="hidden md:block last:hidden" />
							{/each}
						{/if}
					</Breadcrumb.List>
				</Breadcrumb.Root>
			</div>
		</header>

		<div class="p-4 flex flex-1 @container/main bg-stone-50 h-full">
			<div class="w-full">
				{@render children()}
			</div>
		</div>
	</Sidebar.Inset>
</Sidebar.Provider>

<style>
	/* .bg-pattern {
		background-image: radial-gradient(circle at 1px 1px, rgba(0, 0, 0, 0.35) 1px, transparent 0);
		background-size: 20px 20px;
	} */
</style>
