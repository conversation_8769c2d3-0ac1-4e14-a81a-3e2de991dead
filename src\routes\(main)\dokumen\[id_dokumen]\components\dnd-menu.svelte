<script lang="ts">
	import { getDocumentState } from './document-state.svelte';
	import { ControlFrom, controls, draggable } from '@neodrag/svelte';
	import { Button } from '$lib/components/ui/button';
	import MailCheck from '@tabler/icons-svelte/icons/mail-check';
	import Pencil from '@tabler/icons-svelte/icons/pencil';
	const states = getDocumentState();
</script>

<div
	class="p-4 rounded-lg shadow-lg bg-white border w-fit fixed top-1/2 z-50"
	{@attach draggable([controls({ allow: ControlFrom.selector('.drag-handle') })])}
>
	<div
		class="drag-handle cursor-grab absolute -top-4 -left-2 bg-white px-2 border shadow rounded-lg"
	>
		⋮⋮
	</div>
	<div class="flex flex-col gap-2">
		<Button
			variant="outline"
			onclick={() => {
				states.mode = 'view';
			}}
		>
			<MailCheck /> Lihat Surat
		</Button>

		<Button
			variant="outline"
			onclick={() => {
				states.mode = 'edit';
			}}
		>
			<Pencil /> Sunting Surat
		</Button>
	</div>
</div>
