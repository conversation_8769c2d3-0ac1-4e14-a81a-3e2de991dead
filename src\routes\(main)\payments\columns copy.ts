import { renderComponent, renderSnippet } from '$lib/components/ui/data-table';
import type { ColumnDef } from '@tanstack/table-core';
import { createRawSnippet } from 'svelte';
import DataTableEmailButton from './data-table-email-button.svelte';
import DataTableActions from './data-table-actions.svelte';

export type Payment = {
	id: string;
	amount: number;
	status: 'Pending' | 'Processing' | 'Success' | 'Failed';
	email: string;
};

export const columns: ColumnDef<Payment>[] = [
	{
		accessorKey: 'status',
		header: 'Status',
		cell: ({ row }) => {
			const statusSnippet = createRawSnippet<[string]>((getStatus) => {
				const status = getStatus();
				return {
					render: () => `<div class="capitalize">${status}</div>`
				};
			});
			return renderSnippet(statusSnippet, row.getValue('status'));
		}
	},
	{
		accessorKey: 'email',
		header: ({ column }) =>
			renderComponent(DataTableEmailButton, {
				onclick: column.getToggleSortingHandler()
			}),
		cell: ({ row }) => {
			const emailSnippet = createRawSnippet<[string]>((getEmail) => {
				const email = getEmail();
				return {
					render: () => `<div class="lowercase">${email}</div>`
				};
			});

			return renderSnippet(emailSnippet, row.getValue('email'));
		}
	},
	{
		accessorKey: 'amount',
		header: () => {
			const amountHeaderSnippet = createRawSnippet(() => {
				return {
					render: () => `<div class="text-right">Amount</div>`
				};
			});
			return renderSnippet(amountHeaderSnippet, '');
		},
		cell: ({ row }) => {
			const amountCellSnippet = createRawSnippet<[string]>((getAmount) => {
				const amount = getAmount();
				return {
					render: () => `<div class="text-right font-medium">${amount}</div>`
				};
			});
			const formatter = new Intl.NumberFormat('en-US', {
				style: 'currency',
				currency: 'USD'
			});

			return renderSnippet(
				amountCellSnippet,
				formatter.format(Number.parseFloat(row.getValue('amount')))
			);
		}
	},
	{
		id: 'actions',
		enableHiding: false,
		cell: ({ row }) => renderComponent(DataTableActions, { id: row.original.id })
	}
];
