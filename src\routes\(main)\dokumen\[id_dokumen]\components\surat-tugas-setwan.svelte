<script lang="ts">
	import { page } from '$app/state';

	import { crossfade } from 'svelte/transition';
	import { flip } from 'svelte/animate';
	const [send, receive] = crossfade({});

	import FormField, { setRemoteFormContext } from '$lib/utils/form-field.svelte';

	import { _Surat, SuratSchema } from '$lib/schema/surat';
	import * as DocumentRemote from '$lib/remote/document.remote';
	import { Schema } from 'effect';

	import { Button } from '$lib/components/ui/button/index.js';
	import * as Popover from '$lib/components/ui/popover/index.js';
	import PlusIcon from '@lucide/svelte/icons/plus';
	import TrashIcon from '@lucide/svelte/icons/trash';
	import EyeIcon from '@tabler/icons-svelte/icons/eye';

	import { getDocumentState } from './document-state.svelte';
	import { toast } from 'svelte-sonner';

	import ArrowUp from '@tabler/icons-svelte/icons/arrow-up';
	import ArrowDown from '@tabler/icons-svelte/icons/arrow-down';
	import EyeClosed from '@tabler/icons-svelte/icons/eye-closed';

	import { _Simple } from '$lib/schema/general';

	setRemoteFormContext(DocumentRemote.create);

	const id = $props.id();
	const states = getDocumentState();
</script>

{#if states.surat.components.kind === 'Surat Tugas Setwan'}
	<form
		id="surat-tugas-dewan-form"
		{...DocumentRemote.create.preflight(SuratSchema.pipe(Schema.standardSchemaV1))}
		oninput={() => DocumentRemote.create.validate()}
	>
		<div class="grid gap-x-4 grid-cols-12">
			<div class="col-span-full text-center underline font-bold uppercase leading-loose">
				Surat Tugas
			</div>
			<div class="col-span-full flex justify-center items-center gap-2 text-center">
				<div>Nomor :</div>
				<FormField
					name="components.nomor_1"
					type="text"
					class="w-16 text-center"
					bind:value={states.surat.components.nomor_1}
					bind:mode={states.mode}
				/>
				<div>/</div>
				<FormField
					name="components.nomor_2"
					type="text"
					class="w-16 text-center"
					emptyDefault
					bind:value={states.surat.components.nomor_2}
					bind:mode={states.mode}
				/>
				<div>/</div>
				<FormField
					name="components.nomor_3"
					type="text"
					class="w-24 text-center"
					bind:value={states.surat.components.nomor_3}
					bind:mode={states.mode}
				/>
				<div>/</div>
				<FormField
					name="components.nomor_4"
					type="text"
					class="w-16 text-center"
					bind:value={states.surat.components.nomor_4}
					bind:mode={states.mode}
				/>
			</div>

			<div class="col-span-full"><br /></div>

			<div class="col-span-2 tracking-wider">
				<Popover.Root open={true}>
					<Popover.Trigger class="flex justify-between items-center w-full ">
						<span>Dasar</span>
						:
					</Popover.Trigger>
					<Popover.Content
						class="w-fit p-0 {states.mode === 'view' ? 'hidden' : ''}"
						side="left"
						sideOffset={12}
						interactOutsideBehavior="ignore"
						trapFocus={false}
					>
						<Button
							variant="outline"
							size="icon"
							onclick={() => {
								if (states.surat.components.kind !== 'Surat Tugas Setwan') return;
								states.surat.components.dasar.push('');
							}}
						>
							<PlusIcon />
						</Button>
					</Popover.Content>
				</Popover.Root>
			</div>
			<div class="col-span-10">
				<div class="flex flex-col gap-1">
					{#each states.surat.components.dasar as _, idx (id + idx)}
						<div class="flex gap-4 items-start">
							<Popover.Root open={true}>
								<Popover.Trigger class="w-4">
									{idx + 1}.
								</Popover.Trigger>
								<Popover.Content
									class="w-fit p-0 {states.mode === 'view' ? 'hidden' : ''}"
									side="left"
									sideOffset={12}
									trapFocus={false}
									interactOutsideBehavior="ignore"
								>
									<Button
										variant="destructive"
										size="icon"
										class="bg-transparent text-rose-600 border-rose-600/30 border hover:text-white"
										onclick={() => {
											if (states.surat.components.kind !== 'Surat Tugas Setwan') return;
											if (states.surat.components.dasar.length === 1) {
												toast.error('Surat harus memiliki minimal 1 dasar.');
												return;
											}
											states.surat.components.dasar.splice(idx, 1);
										}}
									>
										<TrashIcon />
									</Button>
								</Popover.Content>
							</Popover.Root>

							<div class="grow">
								<FormField
									name="components.dasar[{idx}]"
									type="text"
									bind:value={states.surat.components.dasar[idx]}
									class="mb-0"
									bind:mode={states.mode}
								/>
							</div>
						</div>
					{/each}
				</div>
			</div>

			<!-- Kepada -->

			<div class="col-span-full tracking-wider font-bold text-center uppercase my-2 leading-loose">
				Menugaskan :
			</div>

			<div class="col-span-2 tracking-wider flex justify-between">
				<span>Kepada</span>
				:
			</div>

			<div class="col-span-10 flex flex-col leading-tight gap-2">
				{#each states.surat.components.kepada as personil, idx (personil.anggota.id)}
					<div
						class="flex gap-2"
						class:opacity-20={!personil.show}
						class:hidden={!personil.show && states.mode === 'view'}
						in:receive={{ key: personil.anggota.id }}
						out:send={{ key: personil.anggota.id }}
						animate:flip
					>
						<div class="">
							<Popover.Root open={true}>
								<Popover.Trigger class="w-4">
									{idx + 1}.
								</Popover.Trigger>
								<Popover.Content
									class="w-fit p-0 {states.mode === 'view' ? 'hidden' : ''}"
									side="left"
									sideOffset={12}
									trapFocus={false}
									interactOutsideBehavior="ignore"
								>
									<Button
										variant="outline"
										size="icon"
										onclick={() => {
											if (states.surat.components.kind !== 'Surat Tugas Setwan') return;
											const lastShownItemIndex = states.surat.components.kepada.findLastIndex(
												(item) => item.show
											);

											console.log(lastShownItemIndex);

											personil.show = !personil.show;
											if (personil.show) {
												states.surat.components.kepada.splice(
													lastShownItemIndex + 1,
													0,
													states.surat.components.kepada.splice(idx, 1)[0]
												);
											} else
												states.surat.components.kepada.push(
													states.surat.components.kepada.splice(idx, 1)[0]
												);
										}}
									>
										{#if personil.show}
											<EyeIcon />
										{:else}
											<EyeClosed />
										{/if}
									</Button>
								</Popover.Content>
							</Popover.Root>
						</div>

						<div class="grid flex-1 grid-cols-6 gap-x-4">
							<div class="col-span-2 flex justify-between">
								<span>Nama</span>
								:
							</div>
							<div class="col-span-3 uppercase font-bold">
								{personil.anggota.nama}
							</div>

							<div class="col-span-1">
								<Popover.Root open={true}>
									<Popover.Trigger>&nbsp;</Popover.Trigger>
									<Popover.Content
										class="w-fit p-0 bg-transparent border-0 shadow-none {states.mode === 'edit' &&
										personil.show
											? ''
											: 'hidden'}"
										side="right"
										sideOffset={12}
										trapFocus={false}
										interactOutsideBehavior="ignore"
									>
										<Button
											class="shadow-lg"
											variant="outline"
											size="icon"
											onclick={() => {
												if (states.surat.components.kind !== 'Surat Tugas Setwan') return;

												if (idx === 0) return;
												[
													states.surat.components.kepada[idx],
													states.surat.components.kepada[idx - 1]
												] = [
													states.surat.components.kepada[idx - 1],
													states.surat.components.kepada[idx]
												];
											}}
										>
											<ArrowUp />
										</Button>

										<Button
											class="shadow-lg"
											variant="outline"
											size="icon"
											onclick={() => {
												if (states.surat.components.kind !== 'Surat Tugas Setwan') return;

												if (
													idx === states.surat.components.kepada.length - 1 ||
													!states.surat.components.kepada[idx + 1].show
												)
													return;
												[
													states.surat.components.kepada[idx],
													states.surat.components.kepada[idx + 1]
												] = [
													states.surat.components.kepada[idx + 1],
													states.surat.components.kepada[idx]
												];
											}}
										>
											<ArrowDown />
										</Button>
									</Popover.Content>
								</Popover.Root>
							</div>

							{#if personil.anggota.nip && personil.anggota.nip !== ''}
								<div class="col-span-2 flex justify-between tracking-wider">
									<span>Nip</span>
									:
								</div>
								<div class="col-span-4">
									{personil.anggota.nip}
								</div>
							{/if}

							{#if personil.anggota.pangkat_golongan && personil.anggota.pangkat_golongan.id !== 0}
								<div class="col-span-2 flex justify-between">
									<span>Pangkat / Golongan</span>
									:
								</div>
								<div class="col-span-4">
									{personil.anggota.pangkat_golongan.nama_pangkat} /
									{personil.anggota.pangkat_golongan.nama_golongan}
								</div>
							{/if}

							<div class="col-span-2 flex justify-between">
								<span>Jabatan</span>
								:
							</div>
							<div class="col-span-4">
								{personil.jabatan.nama}
								{personil.alat_kelengkapan.tipe_alat_kelengkapan.nama}
								{personil.alat_kelengkapan.nama}
							</div>
						</div>
					</div>
				{/each}
			</div>

			<!-- Untuk -->

			<div class="col-span-full"><br /></div>
			<div class="col-span-2 tracking-wider flex justify-between">
				<span>Untuk</span>
				:
			</div>
			<div class="col-span-10">
				<FormField
					name="components.untuk"
					type="textarea"
					bind:value={states.surat.components.untuk}
					bind:mode={states.mode}
				/>
			</div>

			<div class="col-span-full"><br /></div>

			<div class="col-span-5"></div>
			<div class="col-span-7 text-center leading-loose flex gap-1 justify-center items-center">
				<div>Sorong,</div>
				<FormField
					name="components.pada_tanggal"
					type="date"
					bind:value={states.surat.components.pada_tanggal}
					bind:mode={states.mode}
				/>
			</div>

			<div class="col-span-5"></div>
			<div class="col-span-7">
				{#if page.data.setwanPersonnel}
					<div class="text-center uppercase">Sekretaris Dewan Perwakilan Rakyat Daerah</div>
				{/if}

				<div class="text-center uppercase">Kabupaten Sorong</div>

				<br />
				<br />
				<br />
			</div>

			<div class="col-span-7"></div>
			<div class="col-span-5">
				{#if page.data.setwanPersonnel}
					{@const secretary = page.data.setwanPersonnel.Pimpinan[0]}
					<div class="font-black uppercase underline">
						{secretary.anggota.nama}
					</div>
					<div>{secretary.anggota?.pangkat_golongan?.nama_pangkat}</div>
					<div class="uppercase">NIP. {secretary.anggota?.nip}</div>
				{/if}
			</div>
		</div>
	</form>
{/if}

<style>
	:not(.issue) {
		font-family: 'Times New Roman', Times, serif;
	}
</style>
