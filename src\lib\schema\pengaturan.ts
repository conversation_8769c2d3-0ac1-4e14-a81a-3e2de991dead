/* eslint-disable @typescript-eslint/no-empty-object-type */

import { Schema } from 'effect';
import { _Simple, _Updated, SimpleSchema, UpdatedSchema } from './general';

export const PengaturanSchema = Schema.Struct({
	id: Schema.Number,
	nama: Schema.String,
	deskripsi: Schema.String,

	tipe_pengaturan: SimpleSchema,
	kategori_pengaturan: SimpleSchema,

	value: Schema.String,
	options: Schema.String,

	...UpdatedSchema.fields
});

export interface Pengaturan extends Schema.Schema.Type<typeof PengaturanSchema> {}
export interface PengaturanEncoded extends Schema.Schema.Encoded<typeof PengaturanSchema> {}

export const _Pengaturan: Pengaturan = {
	id: 0,
	nama: '',
	deskripsi: '',
	tipe_pengaturan: _Simple,
	kategori_pengaturan: _Simple,
	value: '',
	options: '',
	..._Updated
};

//

export const PengaturanTipeEntitasSchema = Schema.Struct({
	id: Schema.Number,
	pengaturan: PengaturanSchema,
	tipe_entitas: SimpleSchema,

	value: Schema.String,
	...UpdatedSchema.fields
});

export interface PengaturanTipeEntitas
	extends Schema.Schema.Type<typeof PengaturanTipeEntitasSchema> {}
export interface PengaturanTipeEntitasEncoded
	extends Schema.Schema.Encoded<typeof PengaturanTipeEntitasSchema> {}

export const _PengaturanTipeEntitas: PengaturanTipeEntitas = {
	id: 0,
	pengaturan: _Pengaturan,
	tipe_entitas: _Simple,
	value: '',
	..._Updated
};
