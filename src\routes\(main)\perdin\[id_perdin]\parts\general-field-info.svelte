<script lang="ts">
	import FormField from '$lib/utils/form-field.svelte';
	import { getPerdinState } from '../states.svelte';

	const state = getPerdinState();
</script>

{#if state.perdin}
	<div>
		<FormField
			name="nama"
			type="text"
			label="Kegiatan Perjalanan Dinas"
			value={state.perdin.nama}
			mode={state.mode}
			viewClass="font-semibold text-lg"
		/>

		<FormField
			name="deskripsi"
			label="Deskripsi"
			type="textarea"
			value={state.perdin.deskripsi}
			mode={state.mode}
		/>
	</div>
{/if}
