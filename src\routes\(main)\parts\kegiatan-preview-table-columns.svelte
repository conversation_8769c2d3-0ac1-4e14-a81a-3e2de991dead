<script lang="ts" module>
	import { createRawSnippet } from 'svelte';
	import { renderSnippet } from '$lib/components/ui/data-table';

	import type { ColumnDef } from '@tanstack/table-core';
	import type { PerjalananDinas } from '$lib/schema/perdin';

	export const columns: ColumnDef<PerjalananDinas>[] = [
		{
			id: 'No',
			header: () => 'No.  ',
			cell: ({ row }) =>
				renderSnippet(
					createRawSnippet(() => ({
						render: () => `<div class='text-center'>${row.index + 1}</div>`
					}))
				)
		},
		{
			id: 'Kegiatan',
			accessorKey: 'nama',
			header: () => 'Kegiatan'
		},
		{
			id: 'Tanggal',
			accessorKey: 'tanggal_mulai',
			header: () => 'Tanggal'
		},
		{
			id: 'Status',
			accessorKey: 'status_perdin.nama',
			header: () => 'Status'
		},
		{
			id: 'Aksi',
			header: () => '',
			cell: ({ row }) => renderSnippet(actions, row.original)
		}
	];
</script>

<script lang="ts">
	import { Button } from '$lib/components/ui/button/index.js';
	import ArrowRightIcon from '@lucide/svelte/icons/arrow-right';
	import { browser } from '$app/environment';
</script>

{#snippet actions(perdin: PerjalananDinas)}
	<Button
		size="icon"
		variant="link"
		href="/perdin/{perdin.id}"
		onclick={() => {
			if (browser) localStorage.setItem('perdin', JSON.stringify(perdin));
		}}
	>
		<ArrowRightIcon />
	</Button>
{/snippet}
