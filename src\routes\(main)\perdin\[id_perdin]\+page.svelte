<script lang="ts">
	import * as Tabs from '$lib/components/ui/tabs/index.js';
	import BudgetField from './parts/budget-field.svelte';
	import DocsField from './parts/docs-field.svelte';
	import GeneralField from './parts/general-field.svelte';
	import ParticipantsField from './parts/participants-field.svelte';
	import { setPerdinState } from './states.svelte';

	setPerdinState();
</script>

<Tabs.Root value="general" class="w-full ">
	<Tabs.List>
		<Tabs.Trigger value="general">Informasi Umum</Tabs.Trigger>
		<Tabs.Trigger value="participants">Peserta</Tabs.Trigger>
		<Tabs.Trigger value="budget">Anggaran</Tabs.Trigger>
		<Tabs.Trigger value="docs">Dokumentasi</Tabs.Trigger>
	</Tabs.List>

	<Tabs.Content value="general" class="p-2">
		<GeneralField />
	</Tabs.Content>
	<Tabs.Content value="participants">
		<ParticipantsField />
	</Tabs.Content>
	<Tabs.Content value="budget">
		<BudgetField />
	</Tabs.Content>
	<Tabs.Content value="docs">
		<DocsField />
	</Tabs.Content>
</Tabs.Root>
