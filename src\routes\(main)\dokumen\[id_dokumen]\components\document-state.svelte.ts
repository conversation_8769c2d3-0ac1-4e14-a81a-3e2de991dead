import type { DocumentStatus } from '$lib/schema/literal';
import { type Surat, _Surat } from '$lib/schema/surat';
import { getContext, setContext } from 'svelte';

interface DocumentState {
	status: DocumentStatus;
	surat: Surat;
	mode: 'view' | 'edit';
}

export default class DocumentStateClass implements DocumentState {
	status = $state('Menunggu Verifikasi' as const);
	mode = $state<'view' | 'edit'>('edit');
	surat = $state(_Surat);
}

const DOCUMENT_STATE_KEY = Symbol('@@document-state@@');

export function setDocumentState(): void {
	setContext(DOCUMENT_STATE_KEY, new DocumentStateClass());
}

export function getDocumentState(): DocumentState {
	return getContext<DocumentState>(DOCUMENT_STATE_KEY);
}
