<script lang="ts">
	import { page } from '$app/state';
	import Separator from '$lib/components/ui/separator/separator.svelte';
	import { Button } from '$lib/components/ui/button/index.js';
	import FormField from '$lib/utils/form-field.svelte';
	import PlusIcon from '@lucide/svelte/icons/plus';

	import { formatter, getCreatePerdinState } from '../states.svelte';
	import { _AnggaranKegiatanLog } from '$lib/schema/anggaran';
	import BudgetFieldActivityCard from './budget-field-activity-card.svelte';
	import { toast } from 'svelte-sonner';

	const state = getCreatePerdinState();
</script>

<h2 class="font-semibold">Langkah 3: Aloka<PERSON></h2>

<br />

<div>
	<h3 class="font-semibold text-stone-600 leading-loose">1. Sumber Anggaran <PERSON></h3>
	<p class="text-muted-foreground text-sm tracking-wide">
		<PERSON>lih sumber dana utama untuk seluruh kegiatan perjalanan dinas dalam satu tahun anggaran
	</p>

	<br />

	<FormField
		type="select"
		name="anggaran_tahunan"
		label="Anggaran Tahunan"
		labelClass="leading-loose ps-1 font-semibold"
		bind:value={state.annualBudgetWithLogInfo}
		mode={state.mode}
		selectOptions={page.data.annualBudgetWithLogInfo}
		onValueChange={() => {
			toast.info('Sumber anggaran tahunan diubah.');
			if (state.activityBudgetAssigned.length > 0) toast.info('Rincian anggaran kegiatan diulang');
			state.activityBudgetAssigned = [];
		}}
	/>

	<div
		class="grid grid-cols-3 gap-4 text-center rounded-lg bg-muted/50 p-4 border border-muted-foreground/20"
	>
		<div>
			<p class="text-xs text-muted-foreground tracking-wide">Total Anggaran</p>
			<p class="tracking-wide text-muted-foreground">
				{formatter.format(state.annualBudgetWithLogInfo?.total ?? 0)}
			</p>
		</div>
		<div>
			<p class="text-xs text-muted-foreground tracking-wide">Anggaran Terpakai</p>
			<p class="tracking-wide text-muted-foreground">
				{formatter.format(state.annualBudgetWithLogInfo?.terpakai ?? 0)}
			</p>
		</div>
		<div>
			<p class="text-xs text-muted-foreground tracking-wide">Anggaran Tersedia</p>
			<p class="tracking-wide text-emerald-600 font-semibold">
				{formatter.format(state.annualBudgetWithLogInfo?.tersedia ?? 0)}
			</p>
		</div>
	</div>
</div>

<Separator class="my-4" />

<div>
	<h3 class="font-semibold text-stone-600 leading-loose">2. Rincian Anggaran Kegiatan</h3>

	<p class="text-muted-foreground text-sm tracking-wide">
		Rincikan setiap pengeluaran untuk perjalanan ini, seperti akomodasi, konsumsi, dan transportasi.
	</p>

	<div
		class="flex justify-between items-center p-4 my-2 bg-muted/50 border border-muted-foreground/20 rounded-lg"
	>
		{#if state.annualBudgetWithLogInfo}
			<Button
				size="sm"
				class="my-4 bg-emerald-400"
				onclick={() => {
					state.activityBudgetAssigned.push(_AnggaranKegiatanLog);
				}}
			>
				<PlusIcon /> Tambah Rincian Anggaran
			</Button>

			<div class="text-right">
				<p class="text-xs text-muted-foreground tracking-wide">Total Anggaran yang Dialokasikan</p>
				<p class="tracking-wide text-emerald-600 font-semibold">
					{formatter.format(state.activityBudgetTotal)}
				</p>
			</div>
		{:else}
			<p class="text-xs text-muted-foreground text-center flex-1 tracking-wide">
				Pilih sumber anggaran tahunan terlebih dahulu.
			</p>
		{/if}
	</div>

	<br />

	{#each state.activityBudgetAssigned as _, index (index)}
		<BudgetFieldActivityCard {index} />
	{/each}
</div>
