import { query } from '$app/server';
import type {
	AnggaranKegiatan,
	AnggaranTahunan,
	AnggaranTahunanLog,
	AnggaranTahunanLogInfo
} from '$lib/schema/anggaran';
import { effectfulFetch } from '$lib/utils/fetch';
import { Effect, Schema } from 'effect';

export const annual = query(async () => {
	const getter = effectfulFetch<AnggaranTahunan[]>(`/anggaran_tahunan`);
	const response = await Effect.runPromise(getter);

	if (response.kind !== 'success' || !response.data) return [];

	return response.data;
});

const YearSchema = Schema.Positive.pipe(Schema.standardSchemaV1);

export const annualLog = query(YearSchema, async (year) => {
	const getter = effectfulFetch<AnggaranTahunanLog[]>(`/anggaran_tahunan/log`);
	const response = await Effect.runPromise(getter);

	if (response.kind !== 'success' || !response.data) return [] as AnggaranTahunanLog[];

	return response.data.filter((item) => item.tahun === year);
});

export const annualWithLogInfo = query(async () => {
	const _annual = await annual();
	const _log = await annualLog(new Date().getFullYear());

	let _logInfo: AnggaranTahunanLogInfo[] = [];

	_logInfo = _annual.map((item) => {
		let total = 0;
		let terpakai = 0;
		let tersedia = 0;

		_log.forEach((log) => {
			if (log.anggaran_tahunan.id === item.id) {
				if (log.aksi_finansial.id === 1) total += log.nilai;
				if (log.aksi_finansial.id === 2) terpakai += log.nilai;
			}
		});

		tersedia = total - terpakai;

		return {
			...item,
			total,
			terpakai,
			tersedia
		};
	});

	return _logInfo;
});

/////////////////////////////////////////////////////////////////////////

export const activity = query(async () => {
	const getter = effectfulFetch<AnggaranKegiatan[]>(`/anggaran_kegiatan`);
	const response = await Effect.runPromise(getter);

	if (response.kind !== 'success' || !response.data) return [];

	return response.data;
});
