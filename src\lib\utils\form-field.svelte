<script module>
	export const formKey = Symbol('form-field');

	type RemoteFormType = RemoteForm<
		RemoteFormInput, // TODO = Maybe after RFC become stable
		| { kind: 'success'; data: any }
		| { kind: 'fetch' | 'json' | 'backend' | 'manual'; message: string }
	>;

	export const setRemoteFormContext = (remoteForm: RemoteFormType) => {
		return setContext(formKey, remoteForm);
	};

	export function getRemoteFormContext() {
		return getContext<RemoteFormType>(formKey);
	}
</script>

<script
	lang="ts"
	generics="TValue extends string | number | Date | Simple, SelectOptionType extends Simple[] | string[]"
>
	import type { RemoteForm, RemoteFormInput } from '@sveltejs/kit';
	import { setContext, getContext } from 'svelte';

	import { cn, type WithElementRef } from '$lib/utils';

	import type {
		ClassValue,
		HTMLInputAttributes,
		HTMLInputTypeAttribute,
		HTMLTextareaAttributes
	} from 'svelte/elements';
	import type { SelectSingleRootProps } from 'bits-ui';
	import type { Simple } from '$lib/schema/general';

	import { Input } from '$lib/components/ui/input/index.js';
	import { Textarea } from '$lib/components/ui/textarea/index.js';
	import CurrencyInput from '$lib/components/currency-input/currency-input.svelte';

	import * as Select from '$lib/components/ui/select/index.js';
	import CircleAlertIcon from '@lucide/svelte/icons/circle-alert';
	import Label from '$lib/components/ui/label/label.svelte';

	type InputType = Exclude<HTMLInputTypeAttribute, 'file'>;
	type FormFieldType = {
		value: TValue | null;
		name: string;

		emptyDefault?: boolean;
		labelClass?: ClassValue;

		mode?: 'add' | 'view' | 'edit';
		viewClass?: ClassValue;

		label?: string;
		labelExists?: boolean;

		selectOptions?: SelectOptionType;
		itemsSelected?: number[];

		validation?: boolean;
		validationName?: string;

		asText?: boolean;
	};

	type Props = WithElementRef<
		| (Omit<HTMLInputAttributes, 'type' | 'value' | 'name'> & {
				type: InputType | 'currency';
		  } & FormFieldType)
		| (Omit<HTMLTextareaAttributes, 'value' | 'name'> & {
				type: 'textarea';
		  } & FormFieldType)
		| (Omit<SelectSingleRootProps, 'value' | 'type'> & {
				type: 'select';
		  } & FormFieldType)
	>;

	let {
		name,
		id = name,

		emptyDefault = false,
		labelExists = true,
		label,
		placeholder = label,

		type,
		value = $bindable(),

		mode = $bindable('add'),
		viewClass = '',

		labelClass = '',

		selectOptions,
		itemsSelected = [],

		validation = true,
		validationName,

		asText = false,
		...restProps
	}: Props = $props();

	const remoteForm = getRemoteFormContext();

	// Type-safe props for different element types
	const inputProps = $derived(
		type !== 'textarea' && type !== 'select' ? (restProps as HTMLInputAttributes) : {}
	);
	const textareaProps = $derived(type === 'textarea' ? (restProps as HTMLTextareaAttributes) : {});
	const selectProps = $derived(type === 'select' ? (restProps as SelectSingleRootProps) : {});

	class SelectValue {
		#val = $state<string>('');
		constructor() {
			if (value instanceof Object) this.val = JSON.stringify(value);
			else this.#val = value as string;
		}

		get val() {
			return this.#val;
		}

		set val(_value: string) {
			this.#val = _value;

			if (value instanceof Object) value = JSON.parse(_value);
			else if (typeof value === 'string') value = _value;
		}
	}
	const selectValue = new SelectValue();

	const triggerContent = $derived.by(()
		selectOptions?.find((f) => f.id.toString() === (value as Simple)?.id.toString())?.nama ??
			placeholder ??
			'Pilih salah satu.'
	);
</script>

<div>
	{#if labelExists}
		<Label for={id} class={cn('block text-stone-600 tracking-wide leading-relaxed', labelClass)}>
			{label}
		</Label>
	{/if}

	{#if mode === 'view'}
		<p
			class={cn(
				`leading-normal ${type === 'textarea' ? 'min-h-[4rem] text-justify' : ''}`,
				viewClass ?? ''
			)}
		>
			{#if value && value !== ''}
				{type !== 'select' ? value : (value as Simple).nama}
			{:else if emptyDefault}
				&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;
			{:else}
				<span class="text-stone-400 font-extralight italic text-sm">Tidak ada data</span>
			{/if}
		</p>
	{:else if type === 'textarea' && typeof value === 'string'}
		<Textarea
			{name}
			{id}
			{placeholder}
			aria-invalid={!!remoteForm?.issues?.[name]}
			class={cn(`textarea w-full bg-white`, restProps.class ?? '')}
			bind:value
			{...textareaProps}
		/>
	{:else if type === 'select'}
		{#if !selectOptions || selectOptions.length === 0}
			<p class="text-xs text-destructive">Select Options is not Set.</p>
		{:else}
			<input type="hidden" {name} {id} {value} />
			<Select.Root type="single" bind:value={selectValue.val} {...selectProps}>
				<Select.Trigger class="w-full bg-white">{triggerContent}</Select.Trigger>
				<Select.Content>
					{#each selectOptions as option (option instanceof Object ? option.id : option)}
						{#if typeof option === 'object'}
							<Select.Item
								value={JSON.stringify(option)}
								disabled={itemsSelected.includes(option.id)}>{option.nama}</Select.Item
							>
						{:else}
							<Select.Item value={option}>{option}</Select.Item>
						{/if}
					{/each}
				</Select.Content>
			</Select.Root>
		{/if}
	{:else if type === 'currency'}
		<CurrencyInput
			{name}
			{id}
			{placeholder}
			aria-invalid={!!remoteForm?.issues?.[name]}
			class={cn(`input w-full`, restProps.class ?? '')}
			bind:value
			{...inputProps}
		/>
	{:else}
		<Input
			{type}
			{name}
			{id}
			{placeholder}
			aria-invalid={!!remoteForm?.issues?.[name]}
			class={cn(`input w-full`, restProps.class ?? '')}
			bind:value
			{...inputProps}
		/>
	{/if}

	{#if remoteForm && remoteForm.issues && Object.keys(remoteForm.issues).includes(name)}
		<ul class="validation-error py-2 text-xs text-destructive">
			{#each remoteForm.issues[name] as issue, idx (`${name}-${idx}`)}
				<li class="flex gap-2 items-center issue"><CircleAlertIcon size="14" /> {issue.message}</li>
			{/each}
		</ul>
	{/if}
</div>
