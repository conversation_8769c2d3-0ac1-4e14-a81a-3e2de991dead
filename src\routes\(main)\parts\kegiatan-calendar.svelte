<script lang="ts">
	import { Badge } from '$lib/components/ui/badge';
	import CalendarDay from '$lib/components/ui/calendar/calendar-day.svelte';
	import { Calendar } from '$lib/components/ui/calendar/index.js';
	import { IsMobile } from '$lib/hooks/is-mobile.svelte';
	import { getLocalTimeZone, today } from '@internationalized/date';

	import BriefcaseBusiness from '@lucide/svelte/icons/briefcase-business';

	let value = today(getLocalTimeZone());

	const isMobile = new IsMobile();
</script>

<Calendar
	type="single"
	bind:value
	class="@lg/calendar:[--cell-size:--spacing(18)] bg-transparent mx-auto  rounded-lg w-fit"
>
	{#snippet day({ day, outsideMonth })}
		<CalendarDay class="border flex flex-col justify-between p-2 data-[selected]:bg-emerald-400">
			<div class="w-full text-left">{day.day}</div>

			{#if !outsideMonth && day.day % 2 === 0}
				<Badge class="text-xs">
					<BriefcaseBusiness />
					1
				</Badge>
			{/if}
		</CalendarDay>
	{/snippet}
</Calendar>
