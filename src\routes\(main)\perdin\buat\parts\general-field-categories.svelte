<script lang="ts">
	import { page } from '$app/state';
	import type { KategoriPerdin } from '$lib/schema/perdin';

	import FormField from '$lib/utils/form-field.svelte';
	import { getCreatePerdinState } from '../states.svelte';

	import { Label } from '$lib/components/ui/label/index.js';

	const state = getCreatePerdinState();

	state.categories = Object.entries(
		page.data.perdinCategories as Record<string, KategoriPerdin[]>
	).reduce(
		(acc, [nama_tipe_kategori, kategori]) => {
			acc[nama_tipe_kategori] ??= kategori[0];
			return acc;
		},
		{} as Record<string, KategoriPerdin>
	);
</script>

<Label for="kategori" class="block text-stone-800 tracking-wide leading-10"
	>Kategorisasi Perjalanan Dinas</Label
>

{#if state.categories}
	<div class="grid grid-cols-3 gap-2">
		{#each Object.entries(state.categories) as [nama_tipe_kategori, _] (nama_tipe_kategori)}
			<div class="border-e pe-2 me-2 rounded">
				<FormField
					name="kategori"
					label={nama_tipe_kategori}
					type="select"
					selectOptions={page.data.perdinCategories?.[nama_tipe_kategori]}
					bind:value={state.categories[nama_tipe_kategori]}
					mode={state.mode}
				/>
			</div>
		{/each}
	</div>
{:else}
	<div class="col-span-6 text-destructive">Perdin ini tidak dapat dikategorisasi.</div>
{/if}
