{"name": "e-sppd", "version": "0.0.1", "devDependencies": {"@dnd-kit-svelte/core": "^0.0.8", "@dnd-kit-svelte/modifiers": "^0.0.8", "@dnd-kit-svelte/sortable": "^0.0.8", "@dnd-kit-svelte/utilities": "^0.0.8", "@internationalized/date": "^3.8.1", "@lucide/svelte": "^0.544.0", "@neodrag/svelte": "^3.0.0-next.8", "@prettier/plugin-oxc": "^0.0.4", "@sveltejs/adapter-node": "^5.2.14", "@sveltejs/kit": "^2.43.5", "@sveltejs/vite-plugin-svelte": "^6.1.1", "@tabler/icons-svelte": "^3.31.0", "@tailwindcss/typography": "^0.5.16", "@tailwindcss/vite": "^4.1.11", "@tanstack/table-core": "^8.21.3", "@types/d3-scale": "^4.0.8", "@types/d3-shape": "^3.1.7", "bits-ui": "^2.11.0", "clsx": "^2.1.1", "d3-scale": "^4.0.2", "d3-shape": "^3.2.0", "effect": "^3.17.7", "layerchart": "2.0.0-next.27", "mode-watcher": "^1.0.8", "oxlint": "^1.11.0", "prettier": "^3.4.2", "prettier-plugin-svelte": "^3.3.3", "svelte": "^5.39.6", "svelte-check": "^4.3.2", "svelte-sonner": "^1.0.5", "tailwind-merge": "^3.3.1", "tailwind-variants": "^1.0.0", "tailwindcss": "^4.1.13", "tw-animate-css": "^1.3.6", "typescript": "^5.9.2", "vaul-svelte": "^1.0.0-next.7", "vite": "^7.1.7", "vite-plugin-devtools-json": "^0.2.1", "zod": "^3.25.64"}, "private": true, "scripts": {"dev": "vite dev", "build": "vite build", "preview": "vite preview", "prepare": "svelte-kit sync || echo ''", "check": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json", "check:watch": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json --watch", "format": "prettier --write .", "lint": "prettier --check ."}, "type": "module"}