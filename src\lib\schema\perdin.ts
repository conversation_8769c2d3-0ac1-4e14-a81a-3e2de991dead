/* eslint-disable @typescript-eslint/no-empty-object-type */
import { Schema } from 'effect';
import {
	_Assigned,
	_Created,
	_Simple,
	AssignedSchema,
	CreatedSchema,
	SimpleSchema
} from './general';
import { _Personil, PersonilSchema } from './anggota';

export const PerjalananDinasSchema = Schema.Struct({
	id: Schema.Number,
	nama: Schema.NonEmptyString.annotations({
		message: () => 'Nama Kegiatan tidak boleh kosong'
	}),
	deskripsi: Schema.String.annotations({
		message: () => 'Deskripsi tidak boleh kosong'
	}),

	tanggal_mulai: Schema.NonEmptyString.annotations({
		message: () => 'Tanggal Mulai tidak boleh kosong'
	}),
	tanggal_selesai: Schema.NonEmptyString.annotations({
		message: () => 'Tanggal Selesai tidak boleh kosong'
	}),

	status_perdin: SimpleSchema,

	...CreatedSchema.fields
});

export interface PerjalananDinas extends Schema.Schema.Type<typeof PerjalananDinasSchema> {}
export interface PerjalananDinasEncoded
	extends Schema.Schema.Encoded<typeof PerjalananDinasSchema> {}

export const _PerjalananDinas = {
	id: 0,
	nama: '',
	deskripsi: '',
	tanggal_mulai: '',
	tanggal_selesai: '',
	status_perdin: { id: 1, nama: 'Draft' },
	..._Created
};

/////////////////////////////////////////////////////////////////////////////

export const KategoriPerdinSchema = Schema.Struct({
	id: Schema.Number,
	nama: Schema.NonEmptyString,
	tipe_kategori: SimpleSchema
});

export interface KategoriPerdin extends Schema.Schema.Type<typeof KategoriPerdinSchema> {}
export interface KategoriPerdinEncoded extends Schema.Schema.Encoded<typeof KategoriPerdinSchema> {}

export const _KategoriPerdin = {
	id: 0,
	nama: '',
	tipe_kategori: _Simple
};

/////////////////////

export const PesertaPerdinSchema = Schema.Struct({
	id: Schema.Number,
	personil: PersonilSchema,
	perjalanan_dinas: PerjalananDinasSchema,
	peran: SimpleSchema,

	...AssignedSchema.fields
});

export interface PesertaPerdin extends Schema.Schema.Type<typeof PesertaPerdinSchema> {}
export interface PesertaPerdinEncoded extends Schema.Schema.Encoded<typeof PesertaPerdinSchema> {}

export const _PesertaPerdin = {
	id: 0,
	personil: _Personil,
	perjalanan_dinas: _PerjalananDinas,
	peran: _Simple,
	..._Assigned
};
