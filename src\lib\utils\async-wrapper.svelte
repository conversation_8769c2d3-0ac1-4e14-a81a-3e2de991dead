<script lang="ts">
	import type { Snippet } from 'svelte';

	import { Button } from '$lib/components/ui/button/index.js';
	import { Skeleton } from '$lib/components/ui/skeleton/index.js';

	import { cn } from '$lib/utils';

	type SkeletonType = 'card' | 'table' | 'list' | 'blocks';

	interface IProps {
		children: Snippet;
		skeleton: SkeletonType;
		skeletonClass?: string;
		class?: string;
	}

	const { children, skeleton, skeletonClass = '', class: className = '' }: IProps = $props();
</script>

<svelte:boundary>
	{#snippet pending()}
		{#if skeleton === 'card'}
			<Skeleton class={cn('w-full aspect-square rounded-lg', skeletonClass)} />
		{:else if skeleton === 'table'}
			<div class="grid grid-cols-6 gap-2 shadow shadow-white">
				<Skeleton class={cn('h-8 col-span-1', skeletonClass)} />
				<Skeleton class={cn('h-8 col-span-2', skeletonClass)} />
				<Skeleton class={cn('h-8 col-span-1', skeletonClass)} />
				<Skeleton class={cn('h-8 col-span-2', skeletonClass)} />
				<Skeleton class={cn('h-8 col-span-6', skeletonClass)} />
				<Skeleton class={cn('h-8 col-span-6', skeletonClass)} />
			</div>
		{:else if skeleton === 'list'}
			<div class="flex flex-col gap-2 shadow shadow-white">
				<Skeleton class={cn('h-8', skeletonClass)} />
				<Skeleton class={cn('h-8', skeletonClass)} />
				<Skeleton class={cn('h-8', skeletonClass)} />
			</div>
		{:else if skeleton === 'blocks'}
			<div class="grid grid-cols-6 gap-2 shadow shadow-white">
				{#each Array(6) as _, i (i)}
					<Skeleton class={cn('h-16 w-full', skeletonClass)} />
				{/each}
			</div>
		{/if}
	{/snippet}

	{#snippet failed(error, reset)}
		<p class="text-destructive font-light mb-2">There was an error. ({error})</p>
		<Button variant="destructive" onclick={reset}>Try Again</Button>
	{/snippet}

	<div class="{cn('', className)}}">
		{@render children?.()}
	</div>
</svelte:boundary>
