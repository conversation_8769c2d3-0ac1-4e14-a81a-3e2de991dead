import { form, query } from '$app/server';
import {
	PerjalananDinasSchema,
	type KategoriPerdin,
	type PerjalananDinas,
	type PerjalananDinasEncoded
} from '$lib/schema/perdin';
import { effectfulFetch } from '$lib/utils/fetch';
import { Effect, Schema } from 'effect';
import type { CategoriesResponseType } from '../../routes/(main)/perdin/[id_perdin]/states.svelte';
import type { Surat } from '$lib/schema/surat';
import { decodeForm } from '$lib/utils/validation';
import type { SavePayload } from '../../routes/(main)/perdin/buat/states.svelte';

const GetPerdinOptionsSchema = Schema.Struct({
	start_date: Schema.String,
	end_date: Schema.String
}).pipe(Schema.standardSchemaV1);

export const getAllPerdin = query(GetPerdinOptionsSchema, async (options) => {
	const getter = effectfulFetch<PerjalananDinas[]>(
		`/perjalanan_dinas?start=${options.start_date}&end=${options.end_date}`
	);
	const response = await Effect.runPromise(getter);

	if (response.kind !== 'success')
		return { data: [], total_rows: 0 } as { data: PerjalananDinas[]; total_rows: number };
	return response;
});

const IDPerdinSchema = Schema.Number.pipe(Schema.standardSchemaV1);

export const getCategoriesOfPerdin = query(IDPerdinSchema, async (id_perdin) => {
	const getter = effectfulFetch<CategoriesResponseType[]>(
		`/perjalanan_dinas/${id_perdin}/kategori`
	);
	const response = await Effect.runPromise(getter);

	if (response.kind !== 'success' || !response.data) return null;

	const grouped = response.data.reduce(
		(acc, item) => {
			acc[item.kategori_perdin.tipe_kategori.nama] = item;
			return acc;
		},
		{} as Record<string, CategoriesResponseType>
	);
	return grouped;
});

export const deletePerdin = form(async (data) => {
	const id_perdin = data.get('id');
	const getter = effectfulFetch(`/perjalanan_dinas/${id_perdin}`, {
		method: 'DELETE'
	});

	const response = await Effect.runPromise(getter);
	return response;
});

///////////////////// CATEGORIES

export const getAllPerdinCategories = query(async () => {
	const getter = effectfulFetch<KategoriPerdin[]>('/kategori_perdin');
	const response = await Effect.runPromise(getter);

	if (response.kind !== 'success' || !response.data) return [];

	const grouped = Object.groupBy(response.data, (item) => item.tipe_kategori.nama) as Record<
		string,
		KategoriPerdin[]
	>;
	return grouped;
});

///////////////////// DOCUMENTS

export const getDocumentsOfPerdin = query(IDPerdinSchema, async (id_perdin) => {
	const getter = effectfulFetch<Surat[]>(`/perjalanan_dinas/${id_perdin}/surat`);
	const response = await Effect.runPromise(getter);

	if (response.kind !== 'success' || !response.data) return null;

	const grouped = response.data.reduce(
		(acc, item) => {
			acc[item.tipe_surat.nama] ??= [];
			acc[item.tipe_surat.nama].push(item);
			return acc;
		},
		{} as Record<string, Surat[]>
	);

	return grouped;
});

//////////////////// ACTIONS

export const save = form(async (data) => {
	const entries = JSON.parse(data.get('payload') as string) as SavePayload;

	////////////////////////

	if (entries.dprdPersonnel.length === 0)
		return { kind: 'manual' as const, message: 'Peserta DPRD tidak boleh kosong' };

	if (entries.setwanPersonnel.length === 0)
		return { kind: 'manual' as const, message: 'Peserta Setwan tidak boleh kosong' };

	if (!entries.annualBudgetWithLogInfo)
		return { kind: 'manual' as const, message: 'Anggaran Tahunan tidak boleh kosong' };

	if (entries.activityBudgetTotal === 0)
		return { kind: 'manual' as const, message: 'Anggaran Kegiatan tidak boleh kosong' };

	const budgetNotNamed = entries.activityBudgetAssigned.some(
		(item: any) => item.anggaran_kegiatan.id === 0
	);
	if (budgetNotNamed)
		return { kind: 'manual' as const, message: 'Anggaran Kegiatan tidak bisa diidentifikasi.' };

	////////////////////////

	const decoded = decodeForm<PerjalananDinas, PerjalananDinasEncoded>(
		PerjalananDinasSchema,
		entries.perdin
	);

	if (decoded.kind === 'decode_error') return decoded;

	////////////////////////

	// const put_perdin = effectfulFetch<PerjalananDinas>(`/perjalanan_dinas/${decoded.decoded.id}`, {
	// 	method: 'PUT',
	// 	body: JSON.stringify(decoded.decoded)
	// });
	// const response_put_perdin = await Effect.runPromise(put_perdin);
	// if (response_put_perdin.kind !== 'success') return response_put_perdin; TODO = no API yet

	const post_dprd_personnel = effectfulFetch(`/perjalanan_dinas/${decoded.decoded.id}/personil`, {
		method: 'POST',
		body: JSON.stringify(
			entries.dprdPersonnel.map((item: any) => ({
				perjalanan_dinas: { id: decoded.decoded.id },
				personil: { id: item.id },
				peran: { id: 1 } // TODO: Hardcoded
			}))
		)
	});
	const response_post_dprd_personnel = await Effect.runPromise(post_dprd_personnel);
	if (response_post_dprd_personnel.kind !== 'success') return response_post_dprd_personnel;

	const post_setwan_personnel = effectfulFetch(`/perjalanan_dinas/${decoded.decoded.id}/personil`, {
		method: 'POST',
		body: JSON.stringify(
			entries.setwanPersonnel.map((item: any) => ({
				perjalanan_dinas: { id: decoded.decoded.id },
				personil: { id: item.id },
				peran: { id: 2 } // TODO: Hardcoded
			}))
		)
	});
	const response_post_setwan_personnel = await Effect.runPromise(post_setwan_personnel);
	if (response_post_setwan_personnel.kind !== 'success') return response_post_setwan_personnel;

	//////////////////////////////////////////

	const post_budget = effectfulFetch(`/perjalanan_dinas/${decoded.decoded.id}/anggaran_kegiatan`, {
		method: 'POST',
		body: JSON.stringify(
			entries.activityBudgetAssigned.map((item: any) => ({
				perjalanan_dinas: { id: decoded.decoded.id },
				anggaran_kegiatan: { id: item.anggaran_kegiatan.id },
				nilai: item.nilai,
				keterangan: item.keterangan
			}))
		)
	});
	const response_post_budget = await Effect.runPromise(post_budget);
	if (response_post_budget.kind !== 'success') return response_post_budget;

	//////////////////////////////////////////

	return { kind: 'success' as const, data: null };
});

//

export const draft = form(async (data) => {
	const decoded = decodeForm<PerjalananDinas, PerjalananDinasEncoded>(
		PerjalananDinasSchema,
		JSON.parse(data.get('perdin') as string)
	);
	if (decoded.kind === 'decode_error') return decoded;

	const poster = effectfulFetch<PerjalananDinas>(`/perjalanan_dinas`, {
		method: 'POST',
		body: JSON.stringify(decoded.decoded)
	});

	const response = await Effect.runPromise(poster);
	if (response.kind !== 'success') return response;
	if (response.data) response.data.id = response.data.id_perdin; // TODO = Should be fixed in the backend

	///////////////////////////////////////////////////////////////////////////////////////////

	const category = JSON.parse(data.get('kategori') as string) as Record<string, KategoriPerdin>;
	const category_refactor = Object.values(category).map((item) => ({
		perdin: { id: response.data?.id },
		kategori_perdin: { id: item.id }
	}));

	const cat_poster = effectfulFetch(`/perjalanan_dinas/${decoded.decoded.id}/kategori`, {
		method: 'POST',
		body: JSON.stringify(category_refactor)
	});

	const cat_response = await Effect.runPromise(cat_poster);
	if (cat_response.kind !== 'success') return cat_response;

	/////////////////////////////////////////

	// return initial response
	return response;
});
