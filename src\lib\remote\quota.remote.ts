import { query } from '$app/server';
import type { Personil } from '$lib/schema/anggota';
import type { AlatKelengkapan, Simple } from '$lib/schema/general';
import type { <PERSON><PERSON> } from '$lib/schema/kuota';
import { effectfulFetch } from '$lib/utils/fetch';
import { Effect } from 'effect';

enum QuotaType {
	TRAVELING = 1,
	BUDGET = 2
}

export type TravelingGroupQuota = {
	id: number;
	kuota: Kuota[];
	tipe_anggota: Simple;
	alat_kelengkapan: AlatKelengkapan;
	jabatan: Simple;
};

export type TravelingIndividualQuota = {
	id: number;
	kuota: Kuota[];
	personil: Personil;
};

export const getTravelingGroupQuota = query(async () => {
	const getter = effectfulFetch<TravelingGroupQuota[]>(`/kuota/${QuotaType.TRAVELING}/kelompok`);

	const response = await Effect.runPromise(getter);

	if (response.kind !== 'success' || !response.data) return [];

	return response.data;
});

export const getTravelingIndividualQuota = query(async () => {
	const getter = effectfulFetch<TravelingIndividualQuota[]>(
		`/kuota/${QuotaType.TRAVELING}/individu`
	);
	const response = await Effect.runPromise(getter);

	if (response.kind !== 'success' || !response.data) return [];

	return response.data;
});

export const getBudgetGroupQuota = query(async () => {
	const getter = effectfulFetch(`/kuota/${QuotaType.BUDGET}/kelompok`);
	const response = await Effect.runPromise(getter);

	if (response.kind !== 'success' || !response.data) return [];

	return response.data;
});

export const getBudgetIndividualQuota = query(async () => {
	const getter = effectfulFetch(`/kuota/${QuotaType.BUDGET}/individu`);
	const response = await Effect.runPromise(getter);

	if (response.kind !== 'success' || !response.data) return [];

	return response.data;
});
