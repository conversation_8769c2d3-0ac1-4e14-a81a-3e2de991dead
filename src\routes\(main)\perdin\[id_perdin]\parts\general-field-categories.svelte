<script lang="ts">
	import { page } from '$app/state';
	import { getCategoriesOfPerdin } from '$lib/remote/perdin.remote';
	import FormField from '$lib/utils/form-field.svelte';
	import { getPerdinState } from '../states.svelte';

	const state = getPerdinState();
	state.categories = state.perdin ? await getCategoriesOfPerdin(state.perdin.id) : null;
</script>

<h3 class="leading-loose text-muted-foreground text-xs font-light tracking-wide">
	Kate<PERSON><PERSON><PERSON>
</h3>

{#if state.categories}
	<div class="grid grid-cols-6 gap-2">
		{#each Object.entries(state.categories) as [nama_tipe_kategori, _] (nama_tipe_kategori)}
			<div class="border-e pe-2 me-2 rounded">
				<FormField
					name="kategori"
					label={nama_tipe_kategori}
					type="select"
					selectOptions={page.data.perdinCategories?.[nama_tipe_kategori]}
					bind:value={state.categories[nama_tipe_kategori].kategori_perdin}
					mode={state.mode}
				/>
			</div>
		{/each}
	</div>
{:else}
	<div class="col-span-6 text-destructive">Perdin ini belum dikategorisasi.</div>
{/if}
