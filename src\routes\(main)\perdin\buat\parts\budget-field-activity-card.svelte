<script lang="ts">
	import { page } from '$app/state';

	import <PERSON>Field from '$lib/utils/form-field.svelte';
	import TrashIcon from '@lucide/svelte/icons/trash';

	import { getCreatePerdinState } from '../states.svelte';

	import * as Tooltip from '$lib/components/ui/tooltip/index.js';
	import { Button } from '$lib/components/ui/button/index.js';
	import { toast } from 'svelte-sonner';

	const _state = getCreatePerdinState();

	const { index }: { index: number } = $props();

	let legacyValue = $state(0);
</script>

<div class="py-4 border-y border-dashed">
	<div class="flex gap-4 items-center">
		<Tooltip.Provider>
			<Tooltip.Root>
				<Tooltip.Trigger>
					<Button
						variant="outline"
						size="icon"
						class="hover:bg-rose-500 hover:text-white "
						onclick={() => {
							_state.activityBudgetAssigned.splice(index, 1);
							toast.info('Rincian anggaran dibatalkan.');
						}}
					>
						<TrashIcon />
					</Button>
				</Tooltip.Trigger>
				<Tooltip.Content>
					<p>Batalkan anggaran</p>
				</Tooltip.Content>
			</Tooltip.Root>
		</Tooltip.Provider>

		<div class="flex-1">
			<FormField
				label="Asal Anggaran"
				labelClass="text-xs font-light ps-1 leading-relaxed"
				name="asal"
				type="select"
				bind:value={_state.activityBudgetAssigned[index].anggaran_kegiatan}
				mode={_state.mode}
				selectOptions={page.data.activityBudget?.filter(
					(item) => _state.annualBudgetWithLogInfo?.id === item.anggaran_tahunan.id
				)}
				itemsSelected={_state.selectedActivityBudgetId}
			/>
		</div>

		<div class="flex-1">
			<FormField
				label="Nominal"
				labelClass="text-xs font-light ps-1 leading-relaxed"
				placeholder=""
				name="nominal"
				type="currency"
				bind:value={_state.activityBudgetAssigned[index].nilai}
				mode={_state.mode}
				onchange={() => {
					if (_state.annualBudgetWithLogInfo)
						if (_state.annualBudgetWithLogInfo.tersedia < _state.activityBudgetTotal) {
							toast.error('Anggaran tidak cukup.');
							_state.activityBudgetAssigned[index].nilai = legacyValue;
						} else legacyValue = _state.activityBudgetAssigned[index].nilai;
				}}
			/>
		</div>
	</div>

	<FormField
		label="Keterangan"
		placeholder="Keterangan rincian penggunaan anggaran."
		labelClass="text-xs font-light ps-1 leading-relaxed"
		name="keterangan"
		type="textarea"
		bind:value={_state.activityBudgetAssigned[index].keterangan}
	/>
</div>
