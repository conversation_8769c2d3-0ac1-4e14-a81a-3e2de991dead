import { effectfulFetch } from '$lib/utils/fetch';
import { Effect } from 'effect';
import type { PageServerLoad } from './$types';
import type { Personil } from '$lib/schema/anggota';
import type { AlatKelengkapan } from '$lib/schema/general';

const PIMPINAN_ID = 9; // TODO : Hardcoded
const SETWAN_PIMPINAN_ID = 10; // TODO : Hardcoded
const TIPE_ALAT_KELENGKAPAN_BAGIAN_SETWAN_ID = 2; // TODO : Hardcoded

export const load: PageServerLoad = async () => {
	const getOfficials = effectfulFetch<Personil[]>(`/personil/alat_kelengkapan/${PIMPINAN_ID}`);

	const getSecretariatOfficials = effectfulFetch<Personil[]>(
		`/personil/alat_kelengkapan/${SETWAN_PIMPINAN_ID}`
	);

	const getAllBagianSetwan = effectfulFetch<AlatKelengkapan[]>(
		`/alat_kelengkapan/tipe/${TIPE_ALAT_KELENGKAPAN_BAGIAN_SETWAN_ID}`
	);

	const [officialsResponse, secretariatResponse, allBagianSetwanResponse] = await Effect.runPromise(
		Effect.all([getOfficials, getSecretariatOfficials, getAllBagianSetwan])
	);

	if (
		officialsResponse.kind !== 'success' ||
		secretariatResponse.kind !== 'success' ||
		allBagianSetwanResponse.kind !== 'success'
	)
		return {
			dprdPersonnel: { Pimpinan: [] },
			setwanPersonnel: { Pimpinan: [] },
			divisions: { Bagian: [] }
		};

	return {
		dprdPersonnel: { Pimpinan: officialsResponse.data },
		setwanPersonnel: { Pimpinan: secretariatResponse.data },
		divisions: { Bagian: allBagianSetwanResponse.data }
	};
};
