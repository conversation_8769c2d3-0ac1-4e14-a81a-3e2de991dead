import type { AnggaranKegiatanLog, AnggaranTahunanLogInfo } from '$lib/schema/anggaran';
import type { Personil } from '$lib/schema/anggota';
import { _PerjalananDinas, type KategoriPerdin, type PerjalananDinas } from '$lib/schema/perdin';
import { getLocalTimeZone, today, type DateValue } from '@internationalized/date';
import type { Mutable } from 'effect/Types';
import { getContext, setContext } from 'svelte';

export const formatter = new Intl.NumberFormat('id-ID', {
	style: 'currency',
	currency: 'IDR',
	minimumFractionDigits: 2,
	maximumFractionDigits: 2
});

export interface SavePayload {
	perdin: PerjalananDinas;
	categories: Record<string, KategoriPerdin>;
	annualBudgetWithLogInfo: AnggaranTahunanLogInfo | null;
	activityBudgetAssigned: AnggaranKegiatanLog[];
	activityBudgetTotal: number;
	dprdPersonnel: Personil[];
	setwanPersonnel: Personil[];
}

export interface CreatePerdinState {
	mode: 'view' | 'add';
	perdin: Mutable<PerjalananDinas>;
	tanggal: { start: DateValue; end: DateValue };
	categories: Record<string, KategoriPerdin>;

	dprdPersonnel: Personil[];
	setwanPersonnel: Personil[];
	annualBudgetWithLogInfo: AnggaranTahunanLogInfo | null;

	activityBudgetAssigned: Mutable<AnggaranKegiatanLog>[];
	selectedActivityBudgetId: number[];
	activityBudgetTotal: number;

	savePayload: () => SavePayload;
}

export default class CreatePerdinStateClass implements CreatePerdinState {
	mode = $state<'view' | 'add'>('add');
	perdin = $state(_PerjalananDinas);
	tanggal = $state({
		start: today(getLocalTimeZone()),
		end: today(getLocalTimeZone())
	});
	categories = $state<Record<string, KategoriPerdin>>({});

	dprdPersonnel = $state<Personil[]>([]);
	setwanPersonnel = $state<Personil[]>([]);
	annualBudgetWithLogInfo = $state<AnggaranTahunanLogInfo | null>(null);

	activityBudgetAssigned = $state<Mutable<AnggaranKegiatanLog>[]>([]);
	selectedActivityBudgetId = $derived(
		this.activityBudgetAssigned.map((item) => item.anggaran_kegiatan.id)
	);
	activityBudgetTotal = $derived(
		this.activityBudgetAssigned.reduce((acc, item) => acc + item.nilai, 0)
	);

	constructor() {
		$effect(() => {
			this.perdin.tanggal_mulai = this.tanggal.start ? this.tanggal.start.toString() : '';
			this.perdin.tanggal_selesai = this.tanggal.end ? this.tanggal.end.toString() : '';
		});
	}

	savePayload() {
		return {
			perdin: this.perdin,
			categories: this.categories,
			annualBudgetWithLogInfo: this.annualBudgetWithLogInfo,
			activityBudgetAssigned: this.activityBudgetAssigned,
			activityBudgetTotal: this.activityBudgetTotal,
			dprdPersonnel: this.dprdPersonnel,
			setwanPersonnel: this.setwanPersonnel
		};
	}
}

const CREATE_PERDIN_STATE_KEY = Symbol('@@Create-perdin-state@@');

export function setCreatePerdinState(): void {
	setContext(CREATE_PERDIN_STATE_KEY, new CreatePerdinStateClass());
}

export function getCreatePerdinState(): CreatePerdinState {
	return getContext<CreatePerdinState>(CREATE_PERDIN_STATE_KEY);
}
