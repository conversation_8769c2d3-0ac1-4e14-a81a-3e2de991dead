<script lang="ts">
	import { Input } from '$lib/components/ui/input';
	import type { HTMLInputAttributes } from 'svelte/elements';
	import type { WithElementRef } from 'bits-ui';
	import { cn } from '$lib/utils';

	export type InputPriceProps = WithElementRef<HTMLInputAttributes> & {
		currencyFormat?: Intl.NumberFormat;
	};

	let {
		ref = $bindable(null),
		value = $bindable(0),
		currencyFormat = new Intl.NumberFormat('id-ID', {
			style: 'currency',
			currency: 'IDR',
			minimumFractionDigits: 0,
			maximumFractionDigits: 0
		}),
		...restProps
	}: InputPriceProps = $props();

	// Extract currency symbol
	let currencySymbol = $derived.by(() => {
		const formatted = currencyFormat.format(0);
		return formatted.replace(/[\d.,\s]/g, '').trim();
	});

	// Display value and focus state
	let displayValue = $state('');
	let isFocused = $state(false);

	// Update display value when numeric value changes
	$effect(() => {
		if (value === 0) {
			displayValue = '';
		} else {
			const formatted = currencyFormat.format(value);
			displayValue = isFocused ? formatted.replace(currencySymbol, '').trim() : formatted;
		}
	});

	function handleInput(event: Event) {
		const target = event.target as HTMLInputElement;
		const inputValue = target.value;

		// Remove all non-digit characters
		const numericString = inputValue.replace(/\D/g, '');

		if (numericString === '') {
			value = 0;
			displayValue = '';
		} else {
			value = parseInt(numericString, 10);
			displayValue = currencyFormat.format(value).replace(currencySymbol, '').trim();
		}

		// Update the input display
		target.value = displayValue;
	}

	function handleFocus(event: FocusEvent) {
		const target = event.target as HTMLInputElement;
		isFocused = true;

		// Remove currency symbol from display
		if (value > 0) {
			displayValue = currencyFormat.format(value).replace(currencySymbol, '').trim();
			target.value = displayValue;
		}

		target.setSelectionRange(target.value.length, target.value.length);
	}

	function handleBlur(event: FocusEvent) {
		const target = event.target as HTMLInputElement;
		isFocused = false;

		// Add currency symbol to display
		if (value > 0) {
			displayValue = currencyFormat.format(value);
			target.value = displayValue;
		}
	}
</script>

<div class="flex-auto">
	<div class="relative flex items-center">
		{#if displayValue === '' && !isFocused}
			<div class="text-muted-foreground pointer-events-none absolute left-3 flex items-center">
				{currencySymbol}
			</div>
		{/if}

		<!-- Hidden input for the actual numeric value -->
		<input type="hidden" bind:value />

		<Input
			type="text"
			value={displayValue}
			placeholder="0"
			autocomplete="off"
			maxlength={22}
			inputmode="numeric"
			class={cn('pl-6', restProps.class)}
			oninput={handleInput}
			onfocus={handleFocus}
			onblur={handleBlur}
			{ref}
			{...restProps}
		/>
	</div>
</div>
